import React, {useEffect, useState} from 'react';
import {
	View,
	StyleSheet,
	Text,
	TouchableOpacity,
	StatusBar,
	KeyboardAvoidingView,
	Platform,
	Pressable,
	TextInput,
} from 'react-native';
import {ScrollView} from 'react-native';
import DropDownPicker from 'react-native-dropdown-picker';
import CLOSE from '../../assets/icons/back.svg';
import UNCHECK from '../../assets/icons/UncheckOutline.svg';
import CHECK from '../../assets/icons/checkboxOutline.svg';
import strings from '../../global/strings';
import Axios from '../../services/api';
import {endpoints} from '../../services/constants';
import useNavigation from '../../hooks/useNavigation';
import colors from '../../global/colors';
import {getScaledFont, height, width} from '../../global/fonts';
import GuestAlert from './GuestAlert';
import {fonts} from '../../assets';
import DateTimePicker from '@react-native-community/datetimepicker';
import {useRoute} from '@react-navigation/native';

const AddNewCustomerPopup = () => {
	const route = useRoute();
	var [currentDateFormat, setCurrentDateFormat] = useState('');
	var itemCount = route?.params?.itemCount;
	const [showDatePicker, setShowDatePicker] = useState(true);
	var [selectedVal, setSelectedVal] = useState('');
	const [formatBirthday, setFormatBirthday] = useState('');
	const navigation = useNavigation();
	const [firstName, setFirstName] = useState('');
	const [lastName, setLastName] = useState('');
	const [streetName, setStreetName] = useState('');
	const [stateName, setStateName] = useState('');
	const [zipCode, setZipCode] = useState('');
	const [companyName, setCompanyName] = useState('');
	const [birthday, setBirthday] = useState(new Date());
	const [cityName, setCityName] = useState('');
	const [emailID, setEmailID] = useState('');
	const [suiteName, setSuite] = useState('');
	const [mobileNumber, setMobileNumber] = useState('');
	const [selectedCountry, setSelectedCountry] = useState([
		{label: 'Canada', value: '+1', key: 'canada'},
		{label: 'US', value: '+1', key: 'us'},
	]);
	const [countryName, setCountryName] = useState('');
	const [isDropDownOpen, setIsDropDownOpen] = useState(false);
	const [isDropDownCountryAddress, setIsDropDownCountryAddress] =
		useState(false);
	const [value, setValue] = useState('');
	const [countryAddress, setCountryAddress] = useState('');
	const [uid, setUID] = useState('');
	const [guests, setGuests] = useState([]);
	const [isAlertVisible, setIsAlertVisible] = useState(false);
	const [isAlertText, setIsAlertText] = useState('');
	const [isEnrolled, setIsEnrolled] = useState(false);

	const formatNumber = number => {
		const maxLength = 4;
		const digitCount = String(number).length;
		const zeroesToAdd = maxLength - digitCount;

		let formattedNumber = '';
		if (number < 0) {
			formattedNumber += '-';
		}
		for (let i = 0; i < zeroesToAdd; i++) {
			formattedNumber += '0';
		}
		formattedNumber += Math.abs(number);
		return formattedNumber;
	};

	const createCustomer = async () => {
		try {
			const headers = {
				Authorization:
					'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6Miwicm9sZSI6Im1lcmNoYW50IiwiaWF0IjoxNzExNTQxMzE2LCJleHAiOjE3MTE2Mjc3MTZ9.CZCajn-1CCQAVWWkdEWqaEnfWSFBcV2_u4Z8F4mmB4M',
				'Content-Type': 'application/json',
			};
			const postData = {
				uid: uid,
				first_name: firstName ?? '',
				email: emailID ?? '',
				is_enroll_to_loyalty_program: isEnrolled,
				country: value ?? 'US',
				mobile: mobileNumber ?? '(232) 345-679',
				street: streetName ?? '',
				apt_suite: suiteName ?? '3/120',
				city: cityName ?? 'Toranto',
				state: stateName ?? 'Ontario',
				zip: zipCode ?? '43256',
				company_name: companyName ?? 'Johnson',
				birth_day: formatBirthday ?? 'May-10-1999',
			};
			const response = await Axios.post(
				endpoints.createCustomer,
				postData,
				{
					headers,
				},
			);
			return response.data;
		} catch (error) {
			throw error;
		}
	};

	const getResponse = async () => {
		try {
			const result = await createCustomer();
			setIsAlertVisible(true);
			setIsAlertText(
				result.message
					? result.message
					: strings.customerWasSuccessfullyCreated,
			);
		} catch (error) {
			setIsAlertText(strings.noResultFound);
		}
	};

	const handleDateChange = (event, selectedDate) => {
		const selectedCurrentDate = selectedDate || birthday;
		setShowDatePicker(false);
		const formattedDate = formatDate(selectedCurrentDate);
		setFormatBirthday(formattedDate);
		setBirthday(selectedCurrentDate);
	};

	const formatCurrentDate = inputDate => {
		const date = new Date(inputDate);
		const options = {month: 'short', day: '2-digit', year: 'numeric'};
		return date
			.toLocaleDateString('en-US', options)
			.replace(',', '')
			.replace(/ /g, '-');
	};

	function formatDate(date) {
		const months = [
			'Jan',
			'Feb',
			'Mar',
			'Apr',
			'May',
			'Jun',
			'Jul',
			'Aug',
			'Sep',
			'Oct',
			'Nov',
			'Dec',
		];
		const month = months[date.getMonth()];
		const day = String(date.getDate()).padStart(2, '0');
		const year = date.getFullYear();
		return `${month}-${day}-${year}`;
	}

	const showDatepicker = () => {
		setShowDatePicker(!showDatePicker);
	};

	const handleEnrollPress = () => {
		setIsEnrolled(!isEnrolled);
	};
	const isValidEmail = email => {
		const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailPattern.test(email);
	};
	const formatUSPhoneNumber = numericValue => {
		return numericValue.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
	};

	const handleEmailChange = text => {
		setEmailID(text);
	};
	useEffect(() => {
		DropDownPicker.setListMode('SCROLLVIEW');
		const currentDateFormat1 = formatCurrentDate(new Date().toString());
		setCurrentDateFormat(currentDateFormat1);
		setUID(formatNumber(itemCount).toString());
		setSelectedVal(value);
		setMobileNumber('');
	}, [value]);

	const handlePhoneNumberChange = text => {
		const numericValue = text.replace(/[^0-9]/g, '');
		if (numericValue.length <= 10) {
			const isUSCountry = selectedVal === 'US' || countryName === 'US';
			if (isUSCountry) {
				setMobileNumber(formatUSPhoneNumber(numericValue));
			} else {
				setMobileNumber(numericValue);
			}
		} else {
			setMobileNumber(numericValue);
		}
	};

	const setStreet = text => {
		const formattedText = text.replace(/[^a-zA-Z0-9\s\-,]/g, '');
		setStreetName(formattedText);
	};

	const handleSave = () => {
		setIsAlertVisible(true);
		if (firstName.trim() === '') {
			setIsAlertText(strings.pleaseEnterFirstName);
		} else if (lastName.trim() === '') {
			setIsAlertText(strings.pleaseEnterLastName);
		} else if (lastName.trim() === '') {
		} else if (value === '') {
			setIsAlertText(strings.pleaseSelectCountry);
		} else if (mobileNumber === '') {
			setIsAlertText(strings.pleaseEnterMobileNumber);
		} else if (emailID.trim() === '' || !isValidEmail(emailID)) {
			setIsAlertText(strings.PleaseEnterValidEmail);
		} else if (countryAddress === '') {
			setIsAlertText(strings.pleaseSelectCountry);
		} else if (streetName === '') {
			setIsAlertText(strings.pleaseEnterStreet);
		} else if (suiteName === '') {
			setIsAlertText(strings.pleaseEnterAptSuite);
		} else if (zipCode === '') {
			setIsAlertText(strings.pleaseEnterZipCode);
		} else if (stateName === '') {
			setIsAlertText(strings.pleaseEnterState);
		} else if (companyName === '') {
			setIsAlertText(strings.pleaseEnterCompanyName);
		} else if (
			formatBirthday.trim() === '' ||
			formatBirthday === currentDateFormat
		) {
			setIsAlertText(strings.pleaseEnterBirthday);
		} else if (
			firstName &&
			lastName &&
			value &&
			streetName &&
			stateName &&
			zipCode &&
			companyName &&
			cityName &&
			emailID &&
			suiteName &&
			mobileNumber &&
			countryAddress &&
			formatBirthday !== currentDateFormat
		) {
			const newGuest = {
				firstName,
				lastName,
				streetName,
				stateName,
				zipCode,
				companyName,
				cityName,
				emailID,
				suiteName,
				mobileNumber,
				value,
				countryAddress,
				formatBirthday,
			};
			setGuests(prevGuests => [...prevGuests, newGuest]);

			getResponse();
		}
	};

	const closeAlert = () => {
		setIsAlertVisible(false);
	};

	const goBack = () => navigation.goBack();

	const textName = (text, setFunction) => {
		const formattedText = text.replace(/[^a-zA-Z\s]/g, '');
		setFunction(formattedText);
	};

	const allowNumberAlphabetWithSplChar = (text, setFunction) => {
		const formattedText = text.replace(
			/[^a-zA-Z0-9\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/g,
			'',
		);
		setFunction(formattedText);
	};

	const dropdownClose = () => {
		setIsDropDownOpen(false);
	};

	const dropdownOpen = () => {
		setIsDropDownOpen(!isDropDownOpen);
	};

	const handleInputChange = item => {
		setValue(item.value);
		setIsDropDownOpen(false);
		setSelectedVal(item.value);
		setMobileNumber('');
		updateMobileNumberFormat(item.value);
	};

	const updateMobileNumberFormat = selectedCountryValue => {
		const numericValue = mobileNumber.replace(/[^0-9]/g, '');
		let formattedNumber = '';
		switch (selectedCountryValue) {
			case 'US':
				formattedNumber = formatUSPhoneNumber(numericValue);
				break;

			default:
				formattedNumber = numericValue;
				break;
		}

		setMobileNumber(formattedNumber);
	};

	const handleCountryAddressChange = item => {
		setCountryAddress(item.value);
		setIsDropDownCountryAddress(false);
	};
	const toggleDropDownCountryAddress = () => {
		setIsDropDownCountryAddress(!isDropDownCountryAddress);
	};

	const closeDropDownCountryAddress = () => {
		setIsDropDownCountryAddress(false);
	};

	const transformedCountries = selectedCountry.map(item => ({
		...item,
		value: item.label,
	}));

	const handleZipCodeChange = text => {
		const numericText = text.replace(/[^0-9]/g, '');
		setZipCode(numericText);
	};

	return (
		<View style={styles.baseContainer}>
			<StatusBar hidden />
			<View style={styles.headerStyle}>
				<Pressable style={styles.closeButton} onPress={goBack}>
					<CLOSE />
				</Pressable>
				<View style={styles.bg}>
					<Text style={styles.textLbl}>
						{itemCount > 0 ? `UID-${uid}` : strings.uidName}
					</Text>
					<View style={styles.verticalLine} />
					<TouchableOpacity
						style={styles.saveButton}
						onPress={handleSave}>
						<Text style={styles.saveTextStyle}>{strings.save}</Text>
					</TouchableOpacity>
				</View>
				<GuestAlert
					visible={isAlertVisible}
					onClose={closeAlert}
					text={isAlertText}
				/>
			</View>
			<View style={styles.lineStyle} />
			<View style={styles.addCustomer}>
				<Text style={styles.headingText}>{strings.addNewCustomer}</Text>
			</View>
			<KeyboardAvoidingView
				behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
				style={{flex: 1}}>
				<ScrollView showsVerticalScrollIndicator={false}>
					<View style={styles.container}>
						{/* first lastName row */}
						<View style={styles.firstNameLastView}>
							<View style={styles.firstLastInsideView}>
								<TextInput
									placeholder={strings.firstName}
									placeholderTextColor={
										colors.placeholderColor
									}
									style={styles.firstName}
									value={firstName}
									onChangeText={text =>
										textName(text, setFirstName)
									}
									keyboardType={'ascii-capable'}
								/>
							</View>
							<View style={styles.firstLastInsideView}>
								<TextInput
									placeholder={strings.lastName}
									placeholderTextColor={
										colors.placeholderColor
									}
									style={styles.firstName}
									value={lastName}
									onChangeText={text =>
										textName(text, setLastName)
									}
									keyboardType={'ascii-capable'}
								/>
							</View>
						</View>
						{/* Mobile Number row */}
						<View style={styles.mobileNumberView}>
							<Text style={styles.mobileNumberHeading}>
								{strings.mobileNumber}
							</Text>
							<DropDownPicker
								open={isDropDownOpen}
								// onClose={true}
								value={value}
								setValue={setValue}
								itemSeparator={true}
								items={transformedCountries}
								closeAfterSelecting={true}
								style={[styles.countryDropdown, {zIndex: 1002}]}
								TickIconComponent={() => null}
								onChangeItem={handleInputChange}
								placeholder={selectedCountry[0].label}
								onPress={dropdownOpen}
								multiple={false}
								onClose={dropdownClose}
								listItemContainerStyle={styles.listItem}
								dropDownContainerStyle={
									styles.dropDownContainerOverlay
								}
								defaultValue={selectedCountry[1].label}
								zIndex={1002}
								dropDownMaxHeight={200}
								dropDownStyle={styles.dropdownStyle}
							/>
							<TextInput
								placeholder={strings.mobileNumber}
								placeholderTextColor={colors.placeholderColor}
								style={styles.phoneNumber}
								keyboardType={'numeric'}
								value={mobileNumber}
								maxLength={14}
								onChangeText={handlePhoneNumberChange}
							/>
						</View>
						{/* Email Id row */}
						<TextInput
							placeholder={strings.emailID}
							placeholderTextColor={colors.placeholderColor}
							style={styles.email}
							keyboardType={'ascii-capable'}
							value={emailID}
							onChangeText={handleEmailChange}
						/>
						{/* Delivery Address  row */}
						<View style={styles.DeliveryAddressView}>
							<Text style={styles.mobileNumberHeading}>
								{strings.deliveryAddres}
							</Text>
							<DropDownPicker
								open={isDropDownCountryAddress}
								value={countryAddress}
								setValue={setCountryAddress}
								itemSeparator={true}
								items={transformedCountries}
								closeAfterSelecting={true}
								style={[styles.countryDropdown, {zIndex: 1001}]}
								TickIconComponent={() => null}
								onChangeItem={handleCountryAddressChange}
								onPress={toggleDropDownCountryAddress}
								multiple={false}
								onClose={closeDropDownCountryAddress}
								listItemContainerStyle={styles.listItem}
								dropDownContainerStyle={
									styles.dropDownContainerOverlay
								}
								placeholder={selectedCountry[1].label}
								zIndex={1002}
								dropDownMaxHeight={200}
								dropDownStyle={styles.dropdownStyle}
							/>
							<TextInput
								placeholder={strings.street}
								placeholderTextColor={colors.placeholderColor}
								style={styles.streetSuiteInput}
								value={streetName}
								maxLength={440}
								keyboardType={'ascii-capable'}
								onChangeText={setStreet}
							/>
							<TextInput
								placeholder={strings.apiSuite}
								placeholderTextColor={colors.placeholderColor}
								style={styles.streetSuiteInput}
								keyboardType={'ascii-capable'}
								value={suiteName}
								onChangeText={text =>
									allowNumberAlphabetWithSplChar(
										text,
										setSuite,
									)
								}
							/>
							<View style={styles.CityStreetZipView}>
								<View style={styles.cityInsideView}>
									<TextInput
										placeholder={strings.city}
										style={styles.CityInput}
										placeholderTextColor={
											colors.placeholderColor
										}
										keyboardType={'ascii-capable'}
										value={cityName}
										onChangeText={text =>
											textName(text, setCityName)
										}
									/>
								</View>
								<View style={styles.viewFlex}>
									<TextInput
										placeholder={strings.state}
										style={styles.StateName}
										placeholderTextColor={
											colors.placeholderColor
										}
										value={stateName}
										keyboardType={'ascii-capable'}
										onChangeText={text =>
											textName(text, setStateName)
										}
									/>
								</View>
								<View style={styles.viewFlex}>
									<TextInput
										placeholder={strings.zip}
										style={styles.ZipInput}
										value={zipCode}
										keyboardType="numeric"
										placeholderTextColor={
											colors.placeholderColor
										}
										maxLength={6}
										onChangeText={handleZipCodeChange}
									/>
								</View>
							</View>
						</View>
						{/* Company row */}
						<TextInput
							placeholder={strings.companyName}
							placeholderTextColor={colors.placeholderColor}
							style={styles.email}
							value={companyName}
							keyboardType={'ascii-capable'}
							onChangeText={text =>
								textName(text, setCompanyName)
							}
						/>
						{/* Birthday */}
						<View style={styles.birthDayView}>
							<TouchableOpacity
								// style={styles.birthDayView}
								onPress={showDatepicker}
							/>
							{showDatePicker ? (
								<DateTimePicker
									testID="dateTimePicker"
									value={birthday}
									mode="date"
									display="default"
									onChange={handleDateChange}
								/>
							) : (
								<Text
									style={{
										alignItems: 'flex-start',
										marginVertical: getScaledFont(10),
										marginHorizontal: getScaledFont(20),
										justifyContent: 'flex-start',
									}}
									onPress={showDatepicker}>
									{formatBirthday}
								</Text>
							)}
						</View>

						{/* Enrollment */}
						<View style={styles.enrollContainer}>
							<TouchableOpacity
								style={styles.imageContainer}
								onPress={handleEnrollPress}>
								{!isEnrolled ? <UNCHECK /> : <CHECK />}
							</TouchableOpacity>
							<Text style={styles.enrollText}>
								{strings.enrollToLoyaltyProgram}
							</Text>
						</View>
					</View>
				</ScrollView>
			</KeyboardAvoidingView>
		</View>
	);
};

const styles = StyleSheet.create({
	birthDayView: {
		alignItems: 'flex-start',
		flexDirection: 'row',
		height: height(68),
		borderWidth: getScaledFont(1),
		paddingVertical: height(12.3),
		marginVertical: getScaledFont(10),
		marginHorizontal: getScaledFont(20),
		borderColor: colors.black,
		justifyContent: 'flex-start',
		borderRadius: 8,
		backgroundColor: colors.white,
	},
	viewFlex: {
		flex: 1,
	},
	dropdownStyle: {
		position: 'absolute',
		top: -200,
	},
	cityInsideView: {
		width: '33%',
		height: height(68),
	},
	bg: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'flex-start',
	},
	addCustomer: {
		justifyContent: 'center',
		position: 'absolute',
		alignSelf: 'center',
		height: height(49),
		marginVertical: getScaledFont(18),
	},
	baseContainer: {
		marginHorizontal: getScaledFont(40),
		marginTop: getScaledFont(20),
		backgroundColor: colors.white,
		borderTopLeftRadius: 8,
		borderTopRightRadius: 8,
		borderBottomLeftRadius: 8,
		borderBottomRightRadius: 8,
		flex: 1,
		marginBottom: getScaledFont(16),
	},
	listItem: {
		height: height(68),
		marginStart: getScaledFont(15),
	},
	dropDownContainerOverlay: {
		position: 'absolute',
		backgroundColor: colors.white,
		overflow: 'scroll',
		zIndex: 2,
	},
	enrollContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'flex-start',
		marginHorizontal: getScaledFont(20),
		marginVertical: getScaledFont(20),
		backgroundColor: colors.white,
	},
	imageContainer: {
		marginRight: getScaledFont(10),
		width: height(24),
		height: height(24),
		backgroundColor: colors.white,
	},
	imageStyle: {
		resizeMode: 'contain',
	},
	enrollText: {
		fontSize: getScaledFont(16),
		fontFamily: fonts.medium,
		lineHeight: getScaledFont(19.36),
		color: colors.black,
		backgroundColor: colors.white,
	},
	closeButton: {
		zIndex: 1,
		marginStart: getScaledFont(47.3),
		width: getScaledFont(32),
		height: getScaledFont(32),
		backgroundColor: colors.white,
	},
	countryDropdown: {
		backgroundColor: colors.white,
		borderColor: colors.black,
		borderWidth: 1,
		borderTopLeftRadius: 8,
		borderTopRightRadius: 8,
		borderBottomRightRadius: 0,
		borderBottomLeftRadius: 0,
		paddingHorizontal: getScaledFont(24),
		height: height(68),
	},
	countryDropdownDropdown: {
		backgroundColor: colors.white,
		borderColor: colors.black,
		borderWidth: 1,
		borderRadius: 8,
		height: height(68),
		alignItems: 'center',
	},
	firstLastInsideView: {
		width: '49%',
		height: height(68),
		backgroundColor: colors.white,
	},
	textLbl: {
		marginEnd: getScaledFont(10),
		fontFamily: fonts.medium,
		fontSize: getScaledFont(15),
		lineHeight: getScaledFont(18),
		textAlign: 'center',
		backgroundColor: colors.white,
	},
	mobileNumberHeading: {
		paddingBottom: 15,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		backgroundColor: colors.white,
	},
	firstNameLastView: {
		flexDirection: 'row',
		marginVertical: getScaledFont(20),
		marginHorizontal: getScaledFont(20),
		justifyContent: 'space-between',
		marginStart: getScaledFont(24),
		backgroundColor: colors.white,
	},
	mobileNumberView: {
		marginVertical: getScaledFont(20),
		marginHorizontal: getScaledFont(20),
		backgroundColor: colors.white,
	},
	DeliveryAddressView: {
		marginVertical: getScaledFont(20),
		marginHorizontal: getScaledFont(20),
	},
	CityStreetZipView: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		backgroundColor: colors.white,
	},
	CityInput: {
		borderWidth: 1,
		borderTopWidth: 0,
		height: height(68),
		borderColor: colors.black,
		justifyContent: 'center',
		flex: 1,
		borderBottomLeftRadius: 8,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	ZipInput: {
		borderTopWidth: 0,
		borderLeftWidth: 0,
		borderWidth: 1,
		borderBottomRightRadius: 8,
		height: height(68),
		borderColor: colors.black,
		justifyContent: 'center',
		flex: 1,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	firstName: {
		height: height(68),
		borderColor: colors.black,
		borderWidth: 1,
		justifyContent: 'center',
		flex: 1,
		borderRadius: 8,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	StateName: {
		height: height(68),
		borderColor: colors.black,
		borderWidth: 1,
		justifyContent: 'center',
		flex: 1,
		borderTopWidth: 0,
		borderLeftWidth: 0,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	lastName: {
		height: height(68),
		borderColor: colors.black,
		borderWidth: 1,
		justifyContent: 'center',
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	countryName: {
		borderTopRightRadius: 8,
		borderTopLeftRadius: 8,
		height: height(68),
		borderColor: 'black',
		borderWidth: 1,
		justifyContent: 'center',
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	streetSuiteInput: {
		marginTop: 0,
		height: height(68),
		borderColor: 'black',
		borderTopWidth: 0,
		borderWidth: 1,
		justifyContent: 'center',
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	email: {
		marginVertical: getScaledFont(10),
		marginHorizontal: getScaledFont(20),
		height: height(68),
		borderColor: colors.black,
		borderWidth: 1,
		justifyContent: 'center',
		borderRadius: 8,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	phoneNumber: {
		marginTop: 0,
		height: height(68),
		borderColor: colors.black,
		borderWidth: 1,
		borderTopWidth: 0,
		justifyContent: 'center',
		borderBottomLeftRadius: 8,
		borderBottomRightRadius: 8,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(19.36),
		padding: getScaledFont(24),
		color: colors.textBlack,
		backgroundColor: colors.white,
	},
	headingText: {
		fontSize: getScaledFont(24),
		color: colors.textBlack,
		fontFamily: fonts.medium,
		textAlign: 'center',
		lineHeight: getScaledFont(29.05),
		backgroundColor: colors.white,
	},
	container: {
		marginHorizontal: getScaledFont(255),
		alignSelf: 'center',
		marginVertical: getScaledFont(20),
	},
	headerViewStyle: {
		flex: 1,
		justifyContent: 'center',
		alignSelf: 'center',
		backgroundColor: colors.white,
	},
	headerStyle: {
		flexDirection: 'row',
		marginHorizontal: getScaledFont(20),
		height: height(78),
		alignItems: 'center',
		justifyContent: 'space-between',
		backgroundColor: colors.white,
	},
	verticalLine: {
		height: height(40),
		width: 1,
		backgroundColor: colors.verticalLineColor,
		marginRight: getScaledFont(10),
		alignSelf: 'center',
	},
	saveButton: {
		backgroundColor: colors.organeColor,
		width: width(174),
		height: height(56),
		justifyContent: 'center',
		alignItems: 'center',
		borderRadius: 4,
		zIndex: 1,
	},
	saveTextStyle: {
		fontSize: getScaledFont(16),
		fontFamily: fonts.medium,
		color: colors.white,
		lineHeight: getScaledFont(24),
		textAlign: 'center',
	},
	lineStyle: {
		borderBottomColor: colors.verticalLineColor,
		borderBottomWidth: 1,
	},
});

export default AddNewCustomerPopup;
