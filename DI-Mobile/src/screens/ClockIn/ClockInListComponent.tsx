import DateTimePicker from '@react-native-community/datetimepicker';
import React, {useEffect, useState} from 'react';
import {FlatList, Pressable, StyleSheet, Text, View} from 'react-native';
import {fonts} from '../../assets';
import Calendar from '../../assets/icons/calendar_month.svg';
import CloseXIcon from '../../assets/icons/close.svg';
import colors from '../../global/colors';
import {getScaledFont, height, width} from '../../global/fonts';
import strings from '../../global/strings';
import useNavigation from '../../hooks/useNavigation';

// Define interfaces for ClockInData and ClockInListProps
interface ClockInData {
	name: string;
	clockInTime: string;
	hoursClockedIn: string;
}

interface ClockInListProps {
	clockInData: ClockInData[];
	selectedDate: Date;
	onDateChange: (date: Date) => void;
	onEndReached: () => void;
}

// Define ClockInList component
const ClockInList: React.FC<ClockInListProps> = ({
	clockInData,
	selectedDate,
	onDateChange,
	onEndReached,
}) => {
	const [showDatePicker, setShowDatePicker] = useState(false);
	const [placeholderText, setPlaceholderText] = useState('Select Date');
	const navigation = useNavigation();

	// Effect hook to set placeholder text
	useEffect(() => {
		setPlaceholderText('Select Date');
	}, []);

	function handleCheck(item: any) {
		return item ? item : 'NA';
	}
	const renderItem = ({item}: {item: any}) => {
		return (
			<View style={styles.row}>
				<View style={styles.nameCell}>
					<Text style={styles.nameText}>{handleCheck(item?.employee_name)}</Text>
				</View>
				<Text style={styles.cell}>{handleCheck(item?.clock_in)}</Text>
				<Text style={styles.cell}>
					{handleCheck(item?.working_hours)}
				</Text>
			</View>
		);
	};

	const handleDateChange = (event: any, date?: Date) => {
		setShowDatePicker(false);
		if (date) {
			onDateChange(date);
			setPlaceholderText(date.toDateString());
		}
	};

	function handleClose() {
		navigation.goBack();
	}

	return (
		<View style={styles.modalBackground}>
			<View style={styles.container}>
				<View style={styles.header}>
					<Pressable onPress={handleClose}>
						<CloseXIcon style={styles.closeIcon} />
					</Pressable>
					<Text style={[styles.title]}>
						{strings.dailyclockinList}
					</Text>
				</View>
				<View style={styles.bottomLineView} />
				<Pressable
					style={styles.calenderBox}
					onPress={() => setShowDatePicker(true)}>
					<Text style={styles.calendarTextStyle}>
						{placeholderText}
					</Text>
					<Calendar style={styles.calendarTextStyle} />
				</Pressable>

				{showDatePicker && (
					<DateTimePicker
						value={selectedDate}
						mode="date"
						display="default"
						onChange={handleDateChange}
						style={styles.calendarView}
					/>
				)}

				<View style={styles.tableContainer}>
					<View style={styles.tableHeader}>
						<Text style={[styles.headerCell]}>
							{strings.staffName}
						</Text>
						<Text style={[styles.headerCell]}>
							{strings.clockInTime}
						</Text>
						<Text style={[styles.headerCell]}>
							{strings.hoursClickedIn}
						</Text>
					</View>
					<View style={styles.listView}>
						<FlatList
							data={clockInData}
							renderItem={renderItem}
							keyExtractor={(item, index) => index.toString()}
							onEndReached={onEndReached}
							onEndReachedThreshold={0.5}
							showsVerticalScrollIndicator={false}
						/>
					</View>
				</View>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	modalBackground: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: colors.backdrop,
	},
	container: {
		height: height(980),
		width: width(877),
		backgroundColor: colors.white,
		borderRadius: getScaledFont(10),
	},
	calendarView: {
		alignSelf: 'flex-start',
		width: width(260),
	},
	listView: {
		flex: 0.9,
	},
	header: {
		flexDirection: 'row',
		alignItems: 'center',
		height: height(104),
	},
	title: {
		textAlign: 'center',
		flex: 1,
		fontFamily: fonts.semiBold,
		fontSize: getScaledFont(32),
		color: colors.carousalTitleColor,
		width: '100%',
	},
	calenderBox: {
		flexDirection: 'row',
		borderRadius: getScaledFont(8),
		borderColor: colors.textBox,
		borderWidth: getScaledFont(1),
		height: height(60),
		width: width(260),
		alignItems: 'center',
		paddingHorizontal: width(16),
		marginEnd: width(20),
		marginLeft: width(72),
		marginTop: height(28),
		marginBottom: height(17),
	},
	calendarTextStyle: {
		flex: 1,
		fontSize: getScaledFont(14),
		fontFamily: fonts.regular,
		color: colors.textBox,
	},
	tableContainer: {
		flex: 1,
		paddingHorizontal: width(72),
	},
	tableHeader: {
		flexDirection: 'row',
		alignItems: 'center',
		height: height(70),
		backgroundColor: colors.tabsBg,
		borderBottomWidth: getScaledFont(2),
		borderBottomColor: colors.borderGrey1,
	},
	headerCell: {
		flex: 1,
		textAlign: 'center',
		fontFamily: fonts.semiBold,
		color: colors.carousalTitleColor,
		fontSize: getScaledFont(18),
	},
	row: {
		flexDirection: 'row',
		height: height(56),
		alignItems: 'center',
		borderBottomWidth: getScaledFont(2),
		borderBottomColor: colors.borderGrey1,
	},
	cell: {
		flex: 1,
		textAlign: 'center',
		color: colors.carousalTitleColor,
	},
	nameCell: {
		flex: 1,
		justifyContent: 'center',
	},
	nameText: {
		color: colors.carousalTitleColor,
		textAlign: 'left',
		paddingLeft: width(37),
	},
	bottomLineView: {
		height: getScaledFont(1),
		backgroundColor: colors.dividerBlack,
	},
	closeIcon: {
		marginLeft: width(36),
		zIndex: 1,
	},
});

export default ClockInList;
