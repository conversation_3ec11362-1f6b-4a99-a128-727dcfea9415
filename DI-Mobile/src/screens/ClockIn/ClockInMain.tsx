import React, {useState, useEffect, useCallback} from 'react';
import {SafeAreaView, StyleSheet} from 'react-native';
import ClockInList from '../../screens/ClockIn/ClockInListComponent';
import axios from 'axios';
import {endpoints} from '../../services/constants';
import {getAuthHeaders} from '../../global/utilities';
import useSpinners from '../../hooks/useSpinners';
import {PAGE_LIMIT} from '../../global/constants';

interface ClockInData {
	name: string;
	clockInTime: string;
	hoursClockedIn: string;
	id: number;
}

const ClockInMain: React.FC = () => {
	const [clockInData, setClockInData] = useState<ClockInData[]>([]);
	const [selectedDate, setSelectedDate] = useState<Date>(new Date());
	const {addOneSpinner, removeOneSpinner} = useSpinners();
	const [page, setPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [isFetching, setIsFetching] = useState(false);

	const getAllStaffList = useCallback(() => {
		const params = {
			limit: PAGE_LIMIT,
			offset: (page - 1) * PAGE_LIMIT,
		};

		addOneSpinner();
		setIsFetching(true);

		axios
			.post(endpoints.getListOfStaffShift, params, {
				headers: getAuthHeaders(),
			})
			.then(response => {
				removeOneSpinner();
				setIsFetching(false);
				const responseData = response?.data?.results;
				setClockInData(responseData);
				if (page === 1) {
					setClockInData(responseData);
				} else {
					setClockInData(prevData => [...prevData, ...responseData]);
				}
				const totalPages = Math.ceil(
					response?.data?.count / PAGE_LIMIT,
				);
				setTotalPages(totalPages);
			})
			.catch(error => {
				removeOneSpinner();
				setIsFetching(false);
			});
	}, [addOneSpinner, removeOneSpinner, page]);

	useEffect(() => {
		getAllStaffList();
	}, [page]);

	const handleDateChange = (date: Date) => {
		setSelectedDate(date);
	};

	const handleEndReached = () => {
		if (!isFetching && page < totalPages) {
			setPage(prevPage => prevPage + 1);
		}
	};

	return (
		<SafeAreaView style={styles.container}>
			<ClockInList
				clockInData={clockInData}
				selectedDate={selectedDate}
				onDateChange={handleDateChange}
				onEndReached={handleEndReached}
			/>
		</SafeAreaView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
});

export default ClockInMain;
