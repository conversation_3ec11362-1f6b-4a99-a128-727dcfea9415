import React, {memo, useEffect, useRef, useState, useTransition} from 'react';
import {FlatList, Keyboard, StyleSheet, Text, View} from 'react-native';
import {TouchableOpacity} from 'react-native-gesture-handler';
import Animated, {useSharedValue, withSpring} from 'react-native-reanimated';
import {fonts} from '../../assets';
import NoDataFound from '../../assets/icons/NoData.svg';
import Printer from '../../assets/icons/Printer.svg';
import DownArrow from '../../assets/icons/arrow_drop_down.svg';
import colors from '../../global/colors';
import {getScaledFont, height, width} from '../../global/fonts';
import screenNames from '../../global/screenNames';
import {useTheme} from '../../global/theme';
import useNavigation, {useDebounce} from '../../hooks/useNavigation';
import useSpinners from '../../hooks/useSpinners';
import {useAppDispatch, useAppSelector} from '../../store';
import {setModalError} from '../../store/slice/app.slice';
import {
  setActiveOpenedOrder,
  setCurrentOrder,
  setIsRefreshCount,
  setIsRefreshOrder,
} from '../../store/slice/orders.slice';
import {fetchCurrentOrderDetails} from '../../store/thunk/order.thunk';
import FilterJsonData from './FilterJsonData';
import OrderDetails, {OrdersBtns} from './OrderDetails';
import {formatDate_MMDDYYYYTT} from './utils';
import {setLoginToken, setTokenExpired} from '../../store/slice/user.slice';
import storage from '../../services/storage';
import {clearDineIn} from '../../store/slice/home.slice';
import {useTranslation} from 'react-i18next';
import {AllOrdersSetUp} from '../../types/enums';
import {getAuthHeaders, getLoginHeaders} from '../../global/utilities';
import strings from '../../global/strings';
import {convertTimeZone, formatAmountWithComma} from '../../global/constants';
export const PAGE_LIMIT = 30;
export const TableHeader = memo(({line, activeTab}: any) => {
  const {t} = useTranslation();
  const {theme} = useTheme();
  const tableHeaderContents = [
    {label: t('orderID'), ID: 0},
    {label: t('dateTime'), ID: 1},
    {label: t('empolyeeWithOut'), ID: 2},
    {label: t('amount'), ID: 3},
    {label: t('tip'), ID: 4},
    {label: t('customer'), ID: 5},
    {label: t('paymentType'), ID: 6},
    {label: t('tableName'), ID: 7},
    {label: t('type'), ID: 8},
    {label: t('terminal'), ID: 9},
    // {label: 'OrderStatus', ID: 9},
  ];
  const requiredLine = line?.line
    ? [
        orderStyles.topDivider,
        {
          borderBottomColor: theme?.colors.lightBorderColor1,
        },
      ]
    : {};
  const removedTableContents = tableHeaderContents?.filter(item => {
    return item.label != t('tableName');
  });
  const data =
    activeTab != 'To Go' ? tableHeaderContents : removedTableContents;
  return (
    <View
      style={[
        orderStyles.tableContainer,
        requiredLine,
        {
          backgroundColor: theme?.colors.tabBackgroundColor,
        },
      ]}>
      {data?.map(({label, ID}: any) => {
        return (
          <View key={label + ID} style={orderStyles.orderContainer}>
            <Text numberOfLines={2} style={[orderStyles.orderItemHeaderLabel]}>
              {label || ''}
            </Text>
          </View>
        );
      })}
    </View>
  );
});

export default function Orders({search, startDate, endDate}: any) {
  const {
    openedOrderDetails,
    activeOrderTable,
    pendingOrders,
    inProgressOrders,
    completedOrders,
    cancelledOrders,
    upcomingOrders,
    isFilter,
    filterData,
    activeOrder,
    currentOrder,
    isRefreshOrder,
    isRefreshCount,
  } = useAppSelector((state: any) => state.order);
  const {activeCurrency} = useAppSelector((state: any) => state.home);
  const {spinners} = useAppSelector((state: any) => state.app);
  const orderListRef = useRef<FlatList>(null);
  const {theme} = useTheme();
  const {t} = useTranslation();
  const {selectedTimeZone} = useAppSelector(state => state.home);
  // const searchKey = useDebounce(search, 700, undefined);

  useEffect(() => {
    if (orderListRef?.current) {
      orderListRef?.current?.scrollToOffset({animated: false, offset: 0});
    }
  }, [
    activeOrderTable,
    pendingOrders,
    inProgressOrders,
    cancelledOrders,
    completedOrders,
    upcomingOrders,
    filterData,
  ]);
  const checkNull = (str: any) => (str && str?.length > 0 ? str : '-');
  const {activeStaffData} = useAppSelector((state: any) => state.user);
  const {addOneSpinner, removeOneSpinner} = useSpinners();
  const dispatcher = useAppDispatch();
  const arrowRef = useSharedValue(0);
  const [page, setPage] = useState(1);
  const [count, setCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const showDivider =
    filterData?.length > 0 || currentOrder?.length > 0 ? false : true;
  const navigation = useNavigation();

  function expired() {
    dispatcher(setLoginToken(undefined));
    dispatcher(setTokenExpired(false));
    dispatcher(clearDineIn());
    storage.clearAll();
  }

  function handleError(data: any) {
    if (data == AllOrdersSetUp.NETWORK_ERROR) {
      dispatcher(
        setModalError({
          type: AllOrdersSetUp.MESSAGE,
          title: t('netError'),
          label: t('ok'),
          action: () => undefined,
        }),
      );
    } else if (data?.response?.status === 403) {
      dispatcher(
        setModalError({
          type: AllOrdersSetUp.MESSAGE,
          title: t('sessionExpired'),
          label: t('login'),
          action: () => expired(),
        }),
      );
    } else if (data?.response?.status === 500) {
      dispatcher(
        setModalError({
          type: AllOrdersSetUp.MESSAGE,
          title: t('internalServerError'),
          label: t('ok'),
          action: () => undefined,
        }),
      );
    } else {
      dispatcher(
        setModalError({
          type: AllOrdersSetUp.MESSAGE,
          title: data?.response?.data?.details,
          label: t('ok'),
          action: () => undefined,
        }),
      );
    }
  }

  useEffect(() => {
    if (orderListRef?.current) {
      orderListRef?.current?.scrollToOffset({
        animated: false,
        offset: 0,
      });
      setPage(1);
      setTotalPages(1);
      dispatcher(setCurrentOrder([]));
      setIsFetching(false);
      setCount(0);
      getOrderDetails(1);
      dispatcher(setIsRefreshOrder(false));
      dispatcher(setIsRefreshCount(false));
      dispatcher(setActiveOpenedOrder({}));
      Keyboard.dismiss();
    }
  }, [
    activeOrder,
    activeOrderTable,
    isRefreshOrder,
    startDate,
    endDate,
    search,
    isRefreshCount,
  ]);

  async function getOrderDetails(currentPage: number) {
    var newRequest = {};
    addOneSpinner();
    const offset = (currentPage - 1) * PAGE_LIMIT;
    const filteredModel =
      activeOrderTable === AllOrdersSetUp.READY_TO_SERVE
        ? [
            {
              field: AllOrdersSetUp.KITCHEN_STATUS,
              filterType: AllOrdersSetUp.TEXT,
              type: AllOrdersSetUp.EQUALS,
              values: [AllOrdersSetUp.COMPLETED],
            },
          ]
        : activeOrderTable === AllOrdersSetUp.COMPLETED
        ? [
            {
              field: AllOrdersSetUp.STATUS,
              filterType: AllOrdersSetUp.TEXT,
              type: AllOrdersSetUp.EQUALS,
              values: [AllOrdersSetUp.PAID],
            },
          ]
        : [
            {
              field: AllOrdersSetUp.KITCHEN_STATUS,
              filterType: AllOrdersSetUp.TEXT,
              type: AllOrdersSetUp.EQUALS,
              values: [
                FilterJsonData?.subTypeMap[activeOrder][activeOrderTable],
              ],
            },
          ];
    const request = {
      limit: PAGE_LIMIT,
      offset: offset,
      start_date_timestamp:
        startDate === '' || Number.isNaN(startDate) ? undefined : startDate,
      end_date_timestamp:
        endDate === '' || Number.isNaN(endDate) ? undefined : endDate,
      filterModel: filteredModel,
    };
    if (activeOrder !== AllOrdersSetUp.ALL_ORDERS) {
      request.filterModel.push({
        field: AllOrdersSetUp.ORDER__TYPE,
        filterType: AllOrdersSetUp.TEXT,
        type: AllOrdersSetUp.EQUALS,
        values: [
          FilterJsonData?.typeMap[AllOrdersSetUp.ORDER_TYPE][activeOrder],
        ],
      });
    }
    const searchData = {
      search: {
        term: search,
        fields: [
          AllOrdersSetUp.EMPLOYEE,
          AllOrdersSetUp.CUSTOMER,
          AllOrdersSetUp.ID,
        ],
      },
    };
    if (search === '') {
      newRequest = request;
    } else {
      newRequest = {...request, ...searchData};
    }
    const options = {
      headers: getAuthHeaders(),
    };
    const {data, error} = await fetchCurrentOrderDetails(newRequest, options);
    if (error) {
      setIsFetching(false);
      removeOneSpinner();
      handleError(error);
    } else {
      setIsFetching(false);
      removeOneSpinner();
      const totPages = Math.ceil(data?.count / PAGE_LIMIT);
      setTotalPages(totPages);
      setCount(data?.count);
      if (request?.offset === 0) {
        dispatcher(setCurrentOrder(data?.results));
      } else {
        const newDataSet = new Set([...currentOrder, ...data?.results]);
        const newData = Array.from(newDataSet);
        dispatcher(setCurrentOrder(newData));
      }
    }
  }
  const handleEndReached = () => {
    if (!isFetching && page < totalPages) {
      setPage(prevPage => prevPage + 1);
      getOrderDetails(page + 1);
    }
  };

  return (
    <View style={orderStyles.Container}>
      <TableHeader line={showDivider} activeTab={activeOrder} />
      <View style={orderStyles.listWrapper}>
        <FlatList
          data={isFilter ? filterData : currentOrder}
          showsVerticalScrollIndicator={false}
          ref={orderListRef}
          onEndReached={currentOrder?.length < count ? handleEndReached : null}
          renderItem={({item, index}: any) => {
            const convertedTime = convertTimeZone(
              item?.createdAt,
              selectedTimeZone,
            );
            const amountFormate = formatAmountWithComma(item?.total_price)
            const tipAmountFormate = formatAmountWithComma(item?.tip)
            const rotateArrow =
              openedOrderDetails?.id === item?.id ? `${180}deg` : `${0}deg`;
            const requiredColor =
              openedOrderDetails?.id === item?.id
                ? theme?.colors.tabBackgroundColor
                : theme?.colors.appBg;
            const itemlength = filterData?.length || currentOrder?.length;
            const requiredWidth =
              itemlength - 1 === index && openedOrderDetails?.id !== item?.id
                ? 2
                : 0;
            const last =
              itemlength - 1 === index && openedOrderDetails?.id === item?.id
                ? 1
                : 0;
            function handleDetails() {
              navigation.navigate(screenNames.transactionDetails, {
                item: {
                  id: item?.id,
                  reference: item?.id,
                },
              });
            }
            function handlePrint() {
              navigation.navigate(screenNames.printListPreview, {
                data: openedOrderDetails,
              });
            }
            return (
              <React.Fragment key={index + item?.id}>
                <TouchableOpacity
                  onPress={() => {
                    dispatcher(setActiveOpenedOrder(item));
                    arrowRef.value = withSpring(180, {
                      duration: 300,
                    });
                    // arrowRef.value = withTiming(180, {
                    // 	easing: Easing.inOut(Easing.quad),
                    // 	reduceMotion: ReduceMotion.System,
                    // });
                  }}
                  style={[
                    orderStyles.itemContainer,
                    {
                      backgroundColor: requiredColor,
                      borderColor: theme?.colors.veryLightBorderColor,
                      borderBottomWidth: requiredWidth,
                    },
                  ]}>
                  <View
                    style={[
                      orderStyles.arrowContainer,
                      orderStyles.rightContainer,
                    ]}>
                    <Animated.View
                      style={{
                        transform: [
                          {
                            rotate: rotateArrow,
                          },
                        ],
                      }}>
                      <DownArrow width={12} height={12} />
                    </Animated.View>
                    <Text
                      style={[
                        orderStyles.orderItemLabel,
                        {paddingLeft: width(5)},
                      ]}>
                      {item?.id}
                    </Text>
                  </View>
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.orderItemLabel,
                      orderStyles.rightContainer,
                    ]}>
                    {formatDate_MMDDYYYYTT(convertedTime)}
                  </Text>
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.orderItemLabel,
                      orderStyles.rightContainer,
                    ]}
                    numberOfLines={2}>
                    {`${checkNull(item?.employee)}`}
                  </Text>
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.orderItemLabel,
                      orderStyles.rightContainer,
                    ]}>
                    {activeCurrency}
                    {checkNull(amountFormate)}
                  </Text>
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.orderItemLabel,
                      orderStyles.rightContainer,
                    ]}>
                    {activeCurrency}
                    {checkNull(tipAmountFormate)}
                  </Text>
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.orderItemLabel,
                      orderStyles.rightContainer,
                    ]}>
                    {checkNull(item?.customer)}
                  </Text>
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.orderItemLabel,
                      orderStyles.rightContainer,
                      orderStyles.transactionType,
                    ]}>
                    {checkNull(item?.transaction_type)}
                  </Text>
                  {activeOrder != 'To Go' && (
                    <Text
                      style={[
                        orderStyles.orderItemHeaderLabel,
                        orderStyles.orderItemLabel,
                        orderStyles.rightContainer,
                        orderStyles.transactionType,
                      ]}>
                      {checkNull(item?.table_name)}
                    </Text>
                  )}
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.rightContainer,
                    ]}>
                    {checkNull(item?.order_type?.replace('-', ' '))}
                  </Text>
                  <Text
                    style={[
                      orderStyles.orderItemHeaderLabel,
                      orderStyles.orderItemLabel,
                      orderStyles.rightContainer,
                      // orderStyles.orderContainer,
                    ]}>
                    {item?.terminal}
                  </Text>
                  {/* <Text
									style={[
										orderStyles.orderItemHeaderLabel,
										orderStyles.orderItemLabel,
										orderStyles.rightContainer,
									]}>
									{checkNull(item?.status)}
								</Text> */}
                </TouchableOpacity>
                {openedOrderDetails?.id === item?.id ? (
                  <React.Fragment>
                    {openedOrderDetails?.orderItems?.map(
                      (oderDetails: any, ID: any) => {
                        if (oderDetails?.cartItems) {
                          return oderDetails?.cartItems?.map(
                            (dininOderDetails: any) => {
                              return (
                                <OrderDetails
                                  item={dininOderDetails}
                                  key={ID + dininOderDetails?.id}
                                  modifiers={
                                    dininOderDetails?.selectedModifiers || []
                                  }
                                  options={dininOderDetails?.selectedOptions}
                                />
                              );
                            },
                          );
                        }
                        return (
                          <OrderDetails
                            item={oderDetails}
                            key={ID + oderDetails?.id}
                            modifiers={oderDetails?.selectedModifiers || []}
                            options={oderDetails?.selectedOptions}
                          />
                        );
                      },
                    )}
                    {
                      <View style={[orderStyles.cancelWrapper]}>
                        <View style={orderStyles.cancelView}>
                          <Text
                            style={[
                              orderStyles.cancelTxt,
                              {
                                color:
                                  theme?.colors?.textInputPlaceHolderTextColor,
                              },
                            ]}>
                            {`${t('orderNote')}${' : '}`}
                            <Text
                              style={[
                                orderStyles.reasonTxt,
                                {
                                  color: theme?.colors?.textInputFontColor,
                                },
                              ]}>
                              {item?.notes}
                            </Text>
                          </Text>
                        </View>
                      </View>
                    }
                    {activeOrderTable !== AllOrdersSetUp.PENDING_ORDERS &&
                    activeOrderTable !== AllOrdersSetUp.CANCELLED ? (
                      <View
                        style={[
                          orderStyles.bottomDivider,
                          {
                            borderBottomWidth: width(last),
                          },
                        ]}>
                        <TouchableOpacity
                          style={orderStyles.printerView}
                          onPress={handlePrint}>
                          <Printer
                            style={orderStyles.printerIcon}
                            height={getScaledFont(25)}
                            width={getScaledFont(25)}
                          />
                          <Text style={orderStyles.printerTxt}>
                            {t('print')}
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={orderStyles.printerView}
                          onPress={handleDetails}>
                          <Text style={orderStyles.printerTxt}>
                            {t('details')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    ) : activeOrderTable === AllOrdersSetUp.PENDING_ORDERS ? (
                      <OrdersBtns
                        partialPaid={
                          openedOrderDetails?.amount_paid > 0 &&
                          openedOrderDetails?.amount_paid !==
                            openedOrderDetails?.total_price
                        }
                      />
                    ) : activeOrderTable === AllOrdersSetUp.CANCELLED ? (
                      <View
                        style={[
                          orderStyles.cancelWrapper,
                          {
                            borderBottomWidth: width(last),
                          },
                        ]}>
                        <View style={orderStyles.cancelView}>
                          <Text
                            style={[
                              orderStyles.cancelTxt,
                              {
                                color:
                                  theme?.colors?.textInputPlaceHolderTextColor,
                              },
                            ]}>
                            {`${t('cancelReason')}${' : '}`}
                            <Text
                              style={[
                                orderStyles.reasonTxt,
                                {
                                  color: theme?.colors?.textInputFontColor,
                                },
                              ]}>
                              {item?.cancel_reason}
                            </Text>
                          </Text>
                        </View>
                      </View>
                    ) : null}
                  </React.Fragment>
                ) : null}
              </React.Fragment>
            );
          }}
          ListEmptyComponent={
            <View style={orderStyles.noDataFoundContainer}>
              {spinners ? <React.Fragment /> : <NoDataFound />}
            </View>
          }
        />
      </View>
    </View>
  );
}

const orderStyles = StyleSheet.create({
  Container: {
    width: '100%',
  },
  bottomSpace: {
    paddingBottom: height(30),
  },
  noDataFoundContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
  },
  cancelWrapper: {
    borderTopWidth: width(2),
    borderTopColor: colors.hiddenItem,
    borderBottomColor: colors.hiddenItem,
    flexDirection: 'row',
    paddingLeft: width(10),
  },
  transactionType: {
    fontSize: getScaledFont(16),
  },
  arrowContainer: {
    width: width(89),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listWrapper: {
    paddingBottom: '35%',
  },
  cancelView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: width(15),
    marginVertical: width(14),
  },
  printerTxt: {
    fontSize: getScaledFont(18),
    color: colors.carousalTitleColor,
    fontFamily: fonts.medium,
  },
  cancelTxt: {
    fontSize: getScaledFont(18),
    fontFamily: fonts.bold,
  },
  reasonTxt: {
    fontSize: getScaledFont(18),
    fontFamily: fonts.regular,
  },

  bottomDivider: {
    borderTopWidth: width(2),
    borderTopColor: colors.hiddenItem,
    borderBottomColor: colors.hiddenItem,
    flexDirection: 'row-reverse',
    paddingLeft: width(10),
  },
  topDivider: {
    borderBottomWidth: width(2),
  },
  tableContainer: {
    height: height(72),
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopWidth: width(2),
    borderColor: colors.hiddenItem,
  },
  printerView: {
    borderWidth: width(1),
    borderColor: colors.grey,
    width: width(155),
    height: height(61),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: width(12),
    backgroundColor: colors.orderDetailsBG,
    marginStart: width(16),
  },
  printerIcon: {
    marginEnd: width(10),
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: width(2),
    justifyContent: 'space-between',
  },
  orderItemHeaderLabel: {
    fontFamily: fonts.semiBold,
    fontSize: getScaledFont(16),
    width: width(89),
    textAlign: 'center',
  },
  orderItemLabel: {
    fontFamily: fonts.medium,
    fontSize: getScaledFont(14),
  },
  orderContainer: {
    paddingRight: width(15),
  },
  rightContainer: {
    marginRight: width(15),
  },
  column: {
    flexDirection: 'column',
  },
  grow: {
    flexGrow: 1,
  },
});
