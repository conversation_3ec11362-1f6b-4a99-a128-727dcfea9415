import React, {memo, useEffect, useRef, useState} from 'react';
import {getScaledFont, height, width} from '../../global/fonts';
import colors from '../../global/colors';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {fonts} from '../../assets';
import useNavigation from '../../hooks/useNavigation';
import screenNames from '../../global/screenNames';
import {useAppDispatch, useAppSelector} from '../../store';
import {
  setAddedOrderNote,
  setCashFlowScreenName,
  setCurrentActiveTable,
  setDiscountPrice,
  setIsPaymentMode,
  setOrderId,
  setQRCodeData,
  setRemainingBill,
  setTotalBill,
  setmodify,
  updateOrder,
} from '../../store/slice/home.slice';
import Printer from '../../assets/icons/Printer.svg';
import strings from '../../global/strings';
import {StyleSheet, Text, View} from 'react-native';
import {setActiveOrder} from '../../store/slice/orders.slice';
import {useTheme} from '../../global/theme';
import {setShowAmountSplit} from '../../store/slice/amountSplit.slice';
import {fetchFoodCategories} from '../../store/thunk/home.thunk';
import {useTranslation} from 'react-i18next';
import {Transaction} from '../../types/enums';
import {getAllCancelOrderReasons} from '../../store/thunk/order.thunk';
import {  Animated, PanResponder, Dimensions } from 'react-native';
import CancelReasonModal from './OrderModel/CancelReasonModel';
import DeleteIcon from '../../assets/icons/DeleteImage.svg';


export const OrdersBtns = memo(({partialPaid}: any) => {
  const {t} = useTranslation();
  const navigation = useNavigation();
  const {theme} = useTheme();
  const dispatcher = useAppDispatch();
  const dispatch = useAppDispatch();
  const {activeCurrency} = useAppSelector((state: any) => state.home);
  const {openedOrderDetails} = useAppSelector((state: any) => state.order);
  const [reasons, setReasons] = useState([]);

  const getCancelReasons = async () => {
    const params = {
      limit: 10,
      offset: 0,
      includeCount: true,
    };
    try {
      const {data, error} = await getAllCancelOrderReasons(params);
      if (error) {
        return;
      }
      if (data) {
        const reasonsFromApi = data.map((item: CancelReason) => ({
          title: item.reason,
          isActive: item.is_active,
        }));
        setReasons(reasonsFromApi);
      }
    } catch (err) {}
  };

  useEffect(() => {
    getCancelReasons();
  }, []);

  const Buttons = [
    strings.cancelOrder,
    strings.updateOrder,
    strings.mergeOrder,
    strings.payNow,
    strings.transferOrder,
  ];
  const DisableButtonArray = [
    strings.cancelOrder,
    strings.updateOrder,
    strings.mergeOrder,
    strings.transferOrder,
  ];
  function handleAllOrder() {
    dispatch(
      updateOrder({
        cartItems: openedOrderDetails?.orderItems,
        orderType:
          openedOrderDetails?.order_type === Transaction.DINE_IN ||
          openedOrderDetails?.order_type === Transaction.DINEIN
            ? Transaction.DINEIN
            : Transaction.DRIVE_THROUGH,
      }),
    );
    dispatch(
      setDiscountPrice({
        price: Number(openedOrderDetails?.discount),
        isPercentage: true,
        percentage: Number(openedOrderDetails?.discount_percentage),
      }),
    );
    dispatch(setCurrentActiveTable(openedOrderDetails?.table_id));
    dispatch(setActiveOrder(Transaction.ALL_ORDERS));
  }
  function disableButton(label: any) {
    return DisableButtonArray.includes(label) && partialPaid ? 0.3 : 1;
  }
  function handlePrint() {
    navigation.navigate(screenNames.printListPreview, {
      data: openedOrderDetails,
    });
  }
  return (
    <View
      style={[
        orderDetailStyles.btnWrapper,
        {
          borderTopColor: theme?.colors.lightBorderColor1,
        },
      ]}>
      {Buttons.map(label => (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            switch (label) {
              case Transaction.UPDATED_ORDERS:
                if (partialPaid) {
                  return null;
                } else {
                  dispatcher(
                    updateOrder({
                      cartItems: openedOrderDetails?.orderItems,
                      orderType:
                        openedOrderDetails?.order_type ===
                          Transaction.DINE_IN ||
                        openedOrderDetails?.order_type === Transaction.DINEIN
                          ? Transaction.DINEIN
                          : Transaction.DRIVE_THROUGH,
                    }),
                  );
                  dispatcher(
                    setDiscountPrice({
                      price: Number(openedOrderDetails?.discount),
                      isPercentage: true,
                      percentage: Number(
                        openedOrderDetails?.discount_percentage,
                      ),
                    }),
                  );
                  dispatch(setOrderId(openedOrderDetails?.id?.toString()));
                  dispatcher(setActiveOrder(Transaction.ALL_ORDERS));
                  dispatch(setAddedOrderNote(openedOrderDetails?.notes));
                  dispatcher(fetchFoodCategories());
                  dispatcher(
                    setCurrentActiveTable(openedOrderDetails?.table_id),
                  );
                  navigation.navigate(screenNames.home);
                }
                break;
              case Transaction.MERGE_ORDER:
                if (partialPaid) {
                  return null;
                } else {
                  navigation.navigate(screenNames.mergeModel, {
                    type: Transaction.MERGE_ORDER,
                    id: openedOrderDetails?.id,
                    orderType: openedOrderDetails?.order_type,
                  });
                }
                break;
              case Transaction.TRANSFER_ORDER:
                if (partialPaid) {
                  return null;
                } else {
                  navigation.navigate(screenNames.orderModel, {
                    type: Transaction.TRANSFER_ORDER,
                    id: openedOrderDetails?.id,
                    orderType: openedOrderDetails?.order_type,
                  });
                }
                break;
              case Transaction.CANCEL_ORDER:
                if (partialPaid) {
                  return null;
                } else {
                  navigation.navigate(screenNames.cancelModel, {
                    id: openedOrderDetails?.id,
                    reasons: reasons,
                  });
                }
                break;
              case Transaction.PAY_NOW:
                const modify =
                  openedOrderDetails?.amount_paid > 0 &&
                  openedOrderDetails?.remaining_balance !==
                    openedOrderDetails?.total_price;
                handleAllOrder();
                dispatch(
                  setQRCodeData({
                    id: openedOrderDetails?.id,
                    amount: openedOrderDetails?.remaining_balance,
                    type: Transaction.DINE__IN,
                  }),
                );
                dispatch(setAddedOrderNote(openedOrderDetails?.notes));
                dispatch(setIsPaymentMode(true));
                dispatch(setmodify(modify));
                dispatch(setRemainingBill(true));
                dispatch(
                  setTotalBill(
                    Number(openedOrderDetails?.remaining_balance)?.toFixed(2),
                  ),
                );
                dispatch(setShowAmountSplit(true));
                dispatch(setOrderId(openedOrderDetails?.id?.toString()));
                dispatch(setCashFlowScreenName(Transaction.HOME));
                navigation.navigate(screenNames.home);
                // navigation.navigate(
                // 	screenNames.home,
                // 	{
                // 		data: openedOrderDetails,
                // 		screen: strings.allOrders,
                // 		modify: partialPaid,
                // 	},
                // );
                break;
              default:
                break;
            }
          }}
          key={label}
          style={[
            orderDetailStyles.btn,
            {
              borderColor: theme?.colors.borderColor,
              backgroundColor: theme?.colors.veryLightBorderColor,
            },
            label.includes(Transaction.PAY_NOW)
              ? {
                  backgroundColor: theme?.colors.selectedButtonBackground,
                }
              : {},
          ]}>
          <Text
            style={[
              (orderDetailStyles.labelStyle,
              label.includes(Transaction.PAY_NOW)
                ? {color: theme?.colors.enableButtonTextColor}
                : {
                    color: theme?.colors.textInputFontColor,
                    textAlign: 'center',
                  }),
              {
                opacity: disableButton(label),
              },
            ]}>
            {t(
              label
                .replace(/\s/g, '')
                .replace(/^./, label[0].toLocaleLowerCase()),
            )}
          </Text>
        </TouchableOpacity>
      ))}
      <TouchableOpacity
        style={orderDetailStyles.printerView}
        onPress={handlePrint}>
        <Printer
          width={width(20)}
          height={height(20)}
          style={orderDetailStyles.printerIcon}
        />
        <Text style={orderDetailStyles.printerTxt}>{t('print')}</Text>
      </TouchableOpacity>
    </View>
  );
});

export default function OrderDetails({ item, key, modifiers, options }: any) {
  const swipeThreshold = 200; // Minimum swipe distance
  const pan = useRef(new Animated.Value(0)).current;
  const [modalVisible, setModalVisible] = useState(false);
  const [cancelReasonNote, setCancelReasonNote] = useState(''); 
  const {activeOrderTable}=useAppSelector(state=>state.order)


  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) =>
        Math.abs(gestureState.dx) > 20 && Math.abs(gestureState.dy) < 20,
      onPanResponderMove: (_, gestureState) => {
        // Allow left swipe, clamp the value to -150 only
        if ((activeOrderTable === 'InProgress Orders' || activeOrderTable === 'Pending Orders') && gestureState.dx < 0) {
          const newValue = Math.max(gestureState.dx, -swipeThreshold);
          pan.setValue(newValue);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if ((activeOrderTable === 'InProgress Orders' || activeOrderTable === 'Pending Orders') && gestureState.dx < -swipeThreshold) {
          Animated.timing(pan, {
            toValue: -swipeThreshold, // Swiped far enough to show button
            duration: 300,
            useNativeDriver: true,
          }).start();
        } else {
          Animated.timing(pan, {
            toValue: 0, // Reset to original position
            duration: 300,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const { theme } = useTheme();
  const { activeCurrency } = useAppSelector((state: any) => state.home);

  const handleCancel = () => {
    Animated.timing(pan, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setModalVisible(true); 
    });
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  return item && Object.keys(item).length > 0 ? (
    <View style={{ position: 'relative' }} key={key}>
      <Animated.View
        {...panResponder.panHandlers}
        style={{
          transform: [{ translateX: pan }],
        }}>
        {/* Main item view */}
        <View
          style={[
            orderDetailStyles.warpper,
            {
              backgroundColor: theme?.colors.veryLightBorderColor,
              borderTopColor: theme?.colors.lightBorderColor1,
            },
          ]}>
          <View style={{ flex: 1, flexDirection: 'row' }}>
            <View style={orderDetailStyles.titleWrapper}>
              <Text style={{ color: cancelReasonNote? colors.dangerActive:theme?.colors.textColor }}>
                {`${item?.item_name} x${item?.orderQuantity}`}
              </Text>

          {/* Display cancel reason note if available */}
               {cancelReasonNote ? (
               <Text style={[orderDetailStyles.cancelNote, {
                color: theme?.colors.redColour,
               }]}>
              Cancelled Note: {cancelReasonNote}
               </Text>
               ) : null}

            </View>
            <View style={orderDetailStyles.childWarpper}>
              <Text>{`${activeCurrency}${parseFloat(item?.price).toFixed(2)} x${item?.orderQuantity}`}</Text>
            </View>
            <View style={orderDetailStyles.childWarpper}>
              <Text>{`${activeCurrency}${parseFloat(item?.price * item?.orderQuantity).toFixed(2)}`}</Text>
            </View>
          </View>
        </View>

        {/* Options rendering */}
        {options?.map((optionsItem: any, id: any) => {
          const selectedOption = optionsItem?.selectedOption;
          if (!selectedOption) return null;

          return (
            <View key={id} style={[orderDetailStyles.warpper, orderDetailStyles.transparentBorder]}>
              <View style={orderDetailStyles.titleWrapper}>
                <Text style={orderDetailStyles.modifierCourse}>{`${selectedOption?.option_name} x${item?.orderQuantity}`}</Text>
              </View>
              <View style={orderDetailStyles.modifierLabel}>
                <Text>{`${activeCurrency}${parseFloat(selectedOption?.price || 0).toFixed(2)} x${item?.orderQuantity}`}</Text>
              </View>
              <View style={orderDetailStyles.modifierLabel}>
                <Text>{`${activeCurrency}${parseFloat((selectedOption?.price || 0) * item?.orderQuantity).toFixed(2)}`}</Text>
              </View>
            </View>
          );
        })}       
        {/* Modifiers and Options rendering */}
        {modifiers?.map((extra: any, ID: any) => {
          const last = modifiers.length === ID + 1;
          return (
            <View
              key={ID}
              style={[
                orderDetailStyles.warpper,
                orderDetailStyles.transparentBorder,
                last && !options ? orderDetailStyles.bottomSpace : {},
              ]}>
              <View style={orderDetailStyles.titleWrapper}>
                <Text style={orderDetailStyles.modifierCourse}>{`${extra?.name} x${item?.orderQuantity}`}</Text>
              </View>
              <View style={orderDetailStyles.modifierLabel}>
                <Text>{`${activeCurrency}${parseFloat(extra?.price).toFixed(2)} x${item?.orderQuantity}`}</Text>
              </View>
              <View style={orderDetailStyles.modifierLabel}>
                <Text>{`${activeCurrency}${parseFloat(extra?.price * item?.orderQuantity).toFixed(2)}`}</Text>
              </View>
            </View>
          );
        })}
      </Animated.View>

      {/* Cancel Button when swiped */}
      <Animated.View
        style={{
          position: 'absolute',
          right: 0,
          width: 200, 
          height: '100%', 
          transform: [{ translateX: pan.interpolate({ inputRange: [-swipeThreshold, 0], outputRange: [0, 200], extrapolate: 'clamp' }) }],
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.activeButtonColor,
          opacity: pan.interpolate({
            inputRange: [-swipeThreshold, 0],
            outputRange: [1, 0],
            extrapolate: 'clamp',
          }),
        }}>
        <View style={[orderDetailStyles.cancelButtonContainer,{
          backgroundColor: theme?.colors.enableButtonBackgroundColor,
        }]}>
          <TouchableOpacity onPress={handleCancel} style={orderDetailStyles.buttonContent}>
            <DeleteIcon style={orderDetailStyles.imageStyle} />
            <Text style={[orderDetailStyles.cancelButtonText,{
                  color: theme?.colors.enableButtonTextColor, 
            }]}>{strings.cancel}</Text>
          </TouchableOpacity>

          {/* CancelReasonModal with passing cancel reason note */}
          <CancelReasonModal
            visible={modalVisible}
            setVisible={closeModal}
            setCancelReasonNote={setCancelReasonNote} // Pass cancel reason to the parent
          />
        </View>
      </Animated.View>
    </View>
  ) : null;
}

export const orderDetailStyles = StyleSheet.create({
  warpper: {
    padding: 10,
    flexDirection: 'row',
    width: '100%',
    borderTopWidth: width(2),
  },
  transparentBorder: {
    borderTopWidth: width(2),
    borderTopColor: colors.transparent,
    padding: 2,
  },
  modifierCourse: {
    fontSize: getScaledFont(12),
  },
  mainCourse: {
    fontSize: getScaledFont(14),
    fontFamily: fonts.medium,
    textTransform: 'capitalize',
  },
  printerView: {
    borderWidth: width(1),
    borderColor: colors.grey,
    width: width(155),
    height: height(61),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: width(12),
    backgroundColor: colors.orderDetailsBG,
    marginStart: width(12),
    marginRight: width(14),
  },
  bottomSpace: {
    paddingBottom: 6,
  },
  bottomDivider: {
    borderBottomWidth: width(2),
    borderBottomColor: colors.hiddenItem,
  },
  titleWrapper: {
    width: '30%',
    alignItems: 'center',
  },
  modifierLabel: {
    fontFamily: fonts.medium,
    width: '10%',
    alignItems: 'center',
  },
  childWarpper: {
    width: '10%',
    alignItems: 'center',
  },
  btnWrapper: {
    justifyContent: 'space-between',
    flex: 1,
    flexDirection: 'row',
    borderTopWidth: width(2),
  },
  labelStyle: {
    fontSize: getScaledFont(16),
    fontFamily: fonts.medium,
  },
  payNowColor: {
    color: colors.white,
  },
  activeColor: {
    backgroundColor: colors.accent,
  },
  printerTxt: {
    fontSize: getScaledFont(18),
    color: colors.carousalTitleColor,
    fontFamily: fonts.medium,
  },
  printerIcon: {
    marginEnd: width(10),
  },
  btn: {
    borderWidth: width(1),
    borderColor: colors.grey,
    width: width(155),
    height: height(61),
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: width(12),
    marginStart: width(16),
  },

  cancelButton: {
    width: '100%',
    alignItems: 'center', 
    justifyContent: 'center', 
  },
  cancelButtonContainer: {
    width: 200, 
    alignItems: 'center',
    justifyContent: 'center', 
    flex: 1
  },
  imageStyle: {
    width: width(16),
    height:height(16),
    marginRight: width(10)
  },
  cancelButtonText: {
    fontFamily: fonts.medium, 
    fontSize: getScaledFont(24), 
  },
  buttonContent: {
    flexDirection: 'row', 
    alignItems: 'center',
  },
  cancelNote: {
    fontSize: getScaledFont(13), 
    textAlign: 'center',
    marginTop: height(10),
    fontFamily: fonts.regular
  },
});
