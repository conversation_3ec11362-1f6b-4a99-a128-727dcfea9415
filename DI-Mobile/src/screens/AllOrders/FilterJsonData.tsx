import strings from '../../global/strings';

const subTypeMap = {
	[strings.allOrders]: {
		[strings.pendingOrders]: 'Pending',
		[strings.inProgressOrders]: 'In-Progress',
		[strings.completedOrders]: 'Paid',
		[strings.cancelledOrders]: 'Cancelled',
		[strings.upcoming]: 'Served',
	},
	[strings.headerOpenOrder]: {
		[strings.pendingOrders]: 'Pending',
		[strings.completedOrders]: 'Paid',
		[strings.cancelledOrders]: 'Cancelled',
	},
	[strings.dinein]: {
		[strings.pendingOrders]: 'Pending',
		[strings.completedOrders]: 'Paid',
		[strings.cancelledOrders]: 'Cancelled',
	},
};
const typeMap = {
	[strings.orderType]: {
		[strings.headerOpenOrder]: 'To-Go',
		[strings.dinein]: 'Dine-In',
	},
};

const FilterJsonData = {
	subTypeMap,
	typeMap,
};

export default FilterJsonData;
