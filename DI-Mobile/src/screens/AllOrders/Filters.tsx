import React, { useCallback, useEffect, useState } from 'react';
import {
	I18nManager,
	Keyboard,
	Pressable,
	StyleSheet,
	Text,
	TextInput,
	TouchableOpacity,
	View,
} from 'react-native';
import { fonts } from '../../assets';
import Search from '../../assets/icons/searchicon.svg';
import CalenderModel from '../../components/CustomCalendar/CalenderModel';
import { getScaledFont, height, width } from '../../global/fonts';
import strings from '../../global/strings';
import { useTheme } from '../../global/theme';
import { useAppDispatch, useAppSelector } from '../../store';
import { setIsOpenCalendar } from '../../store/slice/orders.slice';
import { useFocusEffect } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import Close from '../../assets/icons/close.svg';


function Filters({
	setSearch,
	setStartDate,
	setEndDate,
	search,
	startDate,
	endDate,
}: any) {
	const { theme } = useTheme();
	const {t} = useTranslation();
	const dispatch = useAppDispatch();
	const { activeOrderTable, activeOrder, isOpenCalendar } = useAppSelector(
		state => state.order,
	);
	const [tem, setTem] = useState('')
	useEffect(() => {
		Keyboard.dismiss();
		setSearch('');
		setTem('')
		dispatch(setIsOpenCalendar(''));
		setEndDate(NaN), setStartDate(NaN);
	}, [activeOrderTable, activeOrder]);
	useEffect(() => {
		if (isOpenCalendar) {
			Keyboard.dismiss();
		}
	}, [isOpenCalendar]);

	function handleReset() {
		setEndDate(NaN);
		setStartDate(NaN);
		setSearch('');
		setTem('');
		dispatch(setIsOpenCalendar(''));
		Keyboard.dismiss();
	}
	function handleSearch() {
		setSearch(tem)
	}


	const enable =
		(!Number.isNaN(startDate) && startDate !== '') ||
			(!Number.isNaN(endDate) && endDate !== '') ||
			tem?.length > 0
			? true
			: false;
	const enableSearch = tem?.length > 0 ? true : false
	const enableValue = enable ? 1 : 0.5;
	const enableSearchValue = enableSearch ? 1 : 0.5

	useEffect(() => {
		if (!tem) {
			setSearch('');
			setTem('');
			Keyboard.dismiss();
		}
	}, [tem])
	useFocusEffect(useCallback(() => {
		if (search?.length === 0) {
			setTem('')
			setSearch('')
		}
	}, [startDate, endDate, search]))

	return (
    <View style={filterStyles.topWrapper}>
      <View style={filterStyles.mainWrapper}>
        <View>
          <View
            style={[
              filterStyles.wrapper,
              {
                backgroundColor: theme?.colors.appBg,
              },
            ]}>
            <View style={filterStyles.calenderWrapper}>
              {/* <CalenderModel
								startDate={startDate}
								endDate={endDate}
								type={strings.start}
								setTimeStamp={setStartDate}
								placeholder={t('selectStartDateTime')}
							/>
							<View style={{ marginStart: width(20) }} />
							<CalenderModel
								startDate={startDate}
								endDate={endDate}
								type={strings.end}
								setTimeStamp={setEndDate}
								placeholder={t('SelectEndDateTime')}
							/> */}
            </View>
            {/* <Pressable
							style={[
								filterStyles.resetBtn,
								{
									opacity: enableValue,
									borderColor:
										theme?.colors?.enableButtonBackgroundColor,
									backgroundColor:
										theme?.colors?.enableButtonBackgroundColor,
								},
							]}
							onPress={enable ? handleReset : null}>
							<Text
								style={[
									filterStyles.resetTxt,
									{
										color: theme?.colors?.enableButtonTextColor,
									},
								]}>
								{t('reset')}
							</Text>
						</Pressable> */}
          </View>
          <View style={filterStyles.searchContainer}>
            <View
              style={[
                filterStyles.searchView,
                {
                  borderColor: theme?.colors?.textInputPlaceHolderTextColor,
                },
              ]}>
              <TextInput
                placeholder={t('searchByOrderIdEmployeeCustomerName')}
                value={tem}
                placeholderTextColor={
                  theme?.colors.textInputPlaceHolderTextColor
                }
                onFocus={() => dispatch(setIsOpenCalendar(''))}
                style={[
                  filterStyles.searchWrapper,
                  {
                    color: theme?.colors.textInputFontColor,
                    textAlign: I18nManager.isRTL ? 'right' : 'left',
                  },
                ]}
                onBlur={() => Keyboard.dismiss()}
                onChangeText={(text: any) => setTem(text)}
              />
              {tem.length > 0 ? (
                <TouchableOpacity onPress={handleReset}>
                  <Close
                    height={height(30)}
                    width={width(30)}
                    style={filterStyles.searchIcon}
                  />
                </TouchableOpacity>
              ) : (
                <Search style={filterStyles.searchIcon} />
              )}
            </View>
            <Pressable
              style={[
                filterStyles.resetBtn,
                {
                  opacity: enableSearchValue,
                  marginBottom: height(10),
                  borderColor: theme?.colors?.enableButtonBackgroundColor,
                  backgroundColor: theme?.colors?.enableButtonBackgroundColor,
                },
              ]}
              onPress={enableSearch ? handleSearch : null}>
              <Text
                style={[
                  filterStyles.resetTxt,
                  {
                    color: theme?.colors?.enableButtonTextColor,
                  },
                ]}>
                {t('search')}
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </View>
  );
}

const filterStyles = StyleSheet.create({
	wrapper: {
		// height: height(84),
		alignItems: 'center',
		width: '100%',
		justifyContent: 'space-between',
		flexDirection: 'row',
		marginBottom: getScaledFont(10),
		zIndex: 100,
		marginTop: getScaledFont(10),
	},
	calenderWrapper: {
		alignItems: 'center',
		justifyContent: 'flex-start',
		flexDirection: 'row',
		zIndex: 200,
	},
	searchIcon: {
		marginStart: getScaledFont(20),
	},
	mainWrapper: {
		flexDirection: 'row',
	},
	resetBtn: {
		borderWidth: getScaledFont(1),
		height: height(68),
		width: width(160),
		borderRadius: getScaledFont(8),
		marginStart: width(20),
		alignItems: 'center',
		justifyContent: 'center',
	},
	resetTxt: {
		fontSize: getScaledFont(18),
		paddingHorizontal: width(20),
		fontFamily: fonts.medium,
		textAlign: 'center',
	},
	searchContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
	},
	resetWrapper: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'space-between',
		marginVertical: height(10)
	},
	searchView: {
		flexDirection: 'row',
		alignItems: 'center',
		borderWidth: getScaledFont(1),
		borderRadius: getScaledFont(8),
		marginBottom: height(10),
		// borderTopStartRadius: 0,
		// borderBottomStartRadius: 0,
		// borderTopWidth: 0,
		width: width(820),
		height: height(68),
	},
	searchWrapper: {
		fontSize: getScaledFont(16),
		fontFamily: fonts.medium,
		flex: 0.96,
		marginStart: getScaledFont(10),
	},
	topWrapper: {
		// alignSelf: 'center',
		height: 'auto',
		marginHorizontal: width(10),
		zIndex: 1,
	},
});
export default Filters;
