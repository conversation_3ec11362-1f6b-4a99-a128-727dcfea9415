import React, {useCallback, useEffect, useState} from 'react';
import {
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  TextInput,
  I18nManager,
} from 'react-native';
import {fonts} from '../../assets';
import NoDataIcon from '../../assets/icons/NoData.svg';
import Add from '../../assets/icons/add.svg';
import MoreVert from '../../assets/icons/more_vert.svg';
// import TextInput from '../../components/textInput';
import colors from '../../global/colors';
import {getScaledFont, height, width} from '../../global/fonts';
import iconNames from '../../global/iconNames';
import strings from '../../global/strings';
import Axios from '../../services/api';
import {endpoints} from '../../services/constants';
import useNavigation, {useDebounce} from '../../hooks/useNavigation';
import {screenNames} from '../../navigation/constants';
import CategoryCreatedAlert from '../CreateCategory/CategoryCreatedAlert';
import {setModalError} from '../../store/slice/app.slice';
import {commonStyle} from '../DiscountModalScreen/ItemComponentStyles';
import {FlatList} from 'react-native-gesture-handler';
import CustomModal from '../DiscountModalScreen/CustomModal';
import ItemPopup from '../DiscountModalScreen/ItemPopup';
import useSpinners from '../../hooks/useSpinners';
import {useFocusEffect} from '@react-navigation/native';
import {fetchMenuCount} from '../../store/thunk/count.thunk';
import {useAppDispatch} from '../../store';
import {PAGE_LIMIT, capitalizeFirstLetter} from '../../global/constants';
import SearchIcon from '../../assets/icons/searchicon.svg';
import {setLoginToken, setTokenExpired} from '../../store/slice/user.slice';
import storage from '../../services/storage';
import {useTranslation} from 'react-i18next';

const CategoriesSelectionAdSearch = () => {
  const [t] = useTranslation();
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const [data, setData] = useState([]);
  const [action, setAction] = useState('');
  const [isVisible, setisVisble] = useState(false);
  const [categoryId, setCategoryId] = useState(Number);
  const moreData = ['Edit', 'Delete', 'Hide'];
  const [iconPosition, setIconPosition] = useState({x: 0, y: 0});
  const [isMenuVisible, setMenuVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>({});
  const [itemData, setItemData] = useState<any>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDeleteSuccess, setShowDeletedSuccess] = useState(false);
  const {addOneSpinner, removeOneSpinner} = useSpinners();
  const [search, setSearch] = useState('');
  const searchKey = useDebounce(search, 700, undefined);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const [nodata, setNoData] = useState(false);
  const [isSearched, setIsSearched] = useState(false);

  useFocusEffect(
    useCallback(() => {
      let offset = (page - 1) * PAGE_LIMIT;
      if (offset > 0 && isSearched === true && search === '') {
        offset = 0;
        setIsSearched(false);
        setPage(1);
      }
      const request = {
        active: true,
        limit: PAGE_LIMIT,
        includeCount: true,
        offset: search === '' ? offset : 0,
        search: search === '' ? undefined : search,
      };
      getAllCategories(request);
      if (search !== '') {
        setIsSearched(true);
      }
    }, [page, searchKey]),
  );

  useFocusEffect(
    useCallback(() => {
      setSearch('');
    }, []),
  );

  function expired() {
    dispatch(setLoginToken(undefined));
    dispatch(setTokenExpired(false));
    storage.clearAll();
  }

  function handleError(data: any) {
    if (data == t('networkError')) {
      dispatch(
        setModalError({
          type: 'message',
          title: t('netError'),
          label: t('ok'),
          action: () => undefined,
        }),
      );
    } else if (data?.response?.status === 403) {
      dispatch(
        setModalError({
          type: 'message',
          title: t('sessionExpired'),
          label: t('login'),
          action: () => expired(),
        }),
      );
    } else if (data?.response?.status === 500) {
      dispatch(
        setModalError({
          type: 'message',
          title: t('internalServerError'),
          label: t('ok'),
          action: () => undefined,
        }),
      );
    } else {
      dispatch(
        setModalError({
          type: 'message',
          title: data?.response?.data?.details,
          label: t('ok'),
          action: () => undefined,
        }),
      );
    }
  }

  const renderItem = ({item, index}) => {
    const onPress = () => {
      handleEdit(item);
    };
    const onPressDelete = () => {
      handleDelete(item);
    };
    const onPressHide = () => {
      handleHide(item);
    };
    return (
      <View style={styles.itemView}>
        <View style={styles.itemTextStyleContainer}>
          <Text style={styles.itemTextStyle}>
            {capitalizeFirstLetter(item?.name)}
          </Text>
        </View>
        {item.is_default !== true ? (
          <ItemPopup
            handleEdit={onPress}
            handleDelete={onPressDelete}
            hide={onPressHide}
            moreItems={false}
            categories={true}
          />
        ) : null}
      </View>
    );
  };

  const moreRenderItem = ({item, index}: any) => {
    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          handleMoreItemClick(index);
        }}>
        <Text
          style={
            commonStyle(getScaledFont(16), colors.primary, fonts.medium, {
              flex: 1,
              marginVertical: height(12),
              marginHorizontal: width(12),
            }).text
          }>
          {item}
        </Text>
      </TouchableOpacity>
    );
  };

  const handleEdit = (item: any) => {
    setSelectedItem(item);
    navigation.navigate(screenNames.createCategory, {
      create: false,
      data: item,
    });
  };
  const handleDelete = (item: any) => {
    setSelectedItem(item);
    dispatch(
      setModalError({
        type: 'edit',
        title: t('areYouSure'),
        label: t('login'),
        ok: () => deleteCategoryApiCall(item),
      }),
    );
  };

  const handleMoreItemClick = (index: number) => {
    switch (index) {
      case 0:
        setMenuVisible(false);
        setCategoryId(selectedItem.id);
        navigation.navigate(screenNames.createCategory, {
          create: false,
          data: selectedItem,
        });
        break;
      case 1:
        setMenuVisible(false);
        setShowDeleteModal(true);
        break;
      case 2:
        setMenuVisible(false);
        break;
    }
  };

  const handleHide = (item: any) => {
    dispatch(
      setModalError({
        type: 'edit',
        title: t('areYouSureHideCategory'),
        label: t('ok'),
        ok: () => hideCategory(item),
      }),
    );
  };

  const hideCategory = (item: any) => {
    const request = {active: false};
    Axios.patch(endpoints.updateCategory.cast(item?.id), request)
      .then(response => {
        const {data} = response;
        dispatch(
          setModalError({
            type: 'message',
            title: t('hiddenCategoryMessage'),
            label: t('ok'),
            action: () => {
              getAllCategories({
                active: true,
                limit: PAGE_LIMIT,
                includeCount: true,
                offset: (page - 1) * PAGE_LIMIT,
                search: search === '' ? undefined : search,
              });
            },
          }),
        );
        dispatch(fetchMenuCount());
      })
      .catch(err => {
        dispatch(
          setModalError({
            type: 'message',
            title: err?.response?.data?.error,
            label: t('ok'),
          }),
        );
      });
  };

  const handleMoreActionClick = (index: number, event: any, item: any) => {
    const {pageX, pageY} = event.nativeEvent;
    setIconPosition({x: pageX, y: pageY});
    setMenuVisible(true);
    setSelectedItem(item);
  };
  const moreActionView = () => {
    return (
      <Modal
        style={styles.moreActionContainer}
        visible={isMenuVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeMenu}>
        <TouchableWithoutFeedback onPress={closeMenu}>
          <View
            style={[
              styles.moreActionContainer,
              {
                top: iconPosition.y + 20,
                start: iconPosition.x - 88,
              },
            ]}>
            <FlatList data={moreData} renderItem={moreRenderItem} />
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    );
  };
  const closeMenu = () => {
    setMenuVisible(false);
  };
  const deleteCategoryApiCall = (item: {id: any}) => {
    Axios.delete(endpoints.deleteCategory.cast(item?.id))
      .then(() => {
        setShowDeleteModal(false);
        setShowDeletedSuccess(true);
        dispatch(fetchMenuCount());
        dispatch(
          setModalError({
            type: 'message',
            title: t('categoryDeleted'),
            label: t('ok'),
            action: () => {
              getAllCategories({
                active: true,
                limit: PAGE_LIMIT,
                includeCount: true,
                offset: (page - 1) * PAGE_LIMIT,
                search: search === '' ? undefined : search,
              });
            },
          }),
        );
      })
      .catch(exception => {
        setShowDeleteModal(false);
        handleError(exception);
      });
  };

  const handleEndReached = () => {
    if (!isFetching && page < totalPages) {
      setPage(prevPage => prevPage + 1);
    }
  };

  function getAllCategories(params: any) {
    addOneSpinner();
    setIsFetching(true);
    Axios.post(endpoints.getMenuCategories, params)
      .then(response => {
        removeOneSpinner();
        // const { data } = res;
        // setData(data?.results);
        const responseData = response?.data?.results;
        const totalPages = Math.ceil(response?.data?.count / PAGE_LIMIT);
        if (response.data.count > 0) {
          setNoData(false);
        } else {
          setNoData(true);
        }
        removeOneSpinner();
        setIsFetching(false);
        if (params.offset === 0) {
          setData(responseData);
        } else {
          setData(prevData => {
            const newData = responseData.filter(
              item =>
                !prevData.some(existingItem => existingItem?.id === item?.id),
            );
            return [...prevData, ...newData];
          });
        }
        setTotalPages(totalPages);
      })
      .catch(exception => {
        setIsFetching(false);
        removeOneSpinner();
        setNoData(true);
        handleError(exception);
      });
  }

  function handleNoData() {
    return (
      <View style={styles.noDataStyle}>{nodata ? <NoDataIcon /> : null}</View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <SearchIcon height={getScaledFont(24)} width={getScaledFont(24)} />
        <TextInput
          placeholder={t('searchByCategory')}
          placeholderTextColor={colors.searchItemGray}
          style={{
            width: '100%',
            height: height(76),
            fontSize: getScaledFont(18),
            fontFamily: fonts.regular,
            color: colors.primary,
            marginStart: width(8),
            textAlign: I18nManager.isRTL ? 'right' : 'left',
            writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
          }}
          value={search}
          onChangeText={setSearch}
        />
      </View>
      <View style={styles.divider} />

      <TouchableOpacity
        onPress={() => {
          navigation.navigate(screenNames.createCategory, {
            create: true,
          });
        }}
        activeOpacity={1}
        style={styles.createCategoryButton}>
        <Add />
        <Text style={styles.buttonText}>{t('createCategory')}</Text>
      </TouchableOpacity>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={item => item.item_name}
        showsVerticalScrollIndicator={false}
        numColumns={1}
        removeClippedSubviews={true}
        contentContainerStyle={{paddingBottom: 10}}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={handleNoData}
      />
    </View>
  );
};

export default CategoriesSelectionAdSearch;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  categoriesFontStyle: {
    marginStart: width(121),
    flex: 1,
    textAlign: 'center',
    fontFamily: fonts.semiBold,
    fontSize: getScaledFont(24),
    color: colors.primary,
  },
  noDataStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchView: {
    borderBottomColor: colors.buttonGrey,
    borderBottomWidth: 1,
    paddingBottom: height(10),
    paddingStart: width(21),
    paddingTop: height(8),
  },
  searchContainerStyles: {
    justifyContent: 'center',
    height: height(47),
  },
  inputStyle: {
    marginStart: width(8),
    fontFamily: fonts.regular,
    color: colors.primary,
    fontSize: getScaledFont(18),
  },
  createCategoryButton: {
    height: height(68),
    backgroundColor: colors.accent,
    borderRadius: 8,
    marginHorizontal: width(147),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: height(20),
    marginTop: height(40),
    flexDirection: 'row',
  },
  buttonText: {
    color: colors.white,
    fontFamily: fonts.medium,
    fontSize: getScaledFont(20),
    marginStart: width(17),
  },
  homeText: {
    color: colors.white,
    fontFamily: fonts.medium,
    fontSize: getScaledFont(20),
  },
  itemView: {
    borderBottomWidth: 1,
    borderBottomColor: '#C9C9C9',
    flexDirection: 'row',
    paddingVertical: height(28),
    marginHorizontal: width(147),
  },
  itemTextStyle: {
    marginStart: width(10),
    fontFamily: fonts.medium,
    fontSize: getScaledFont(16),
    color: colors.primary,
    textAlign: 'left',
  },
  itemTextStyleContainer: {
    justifyContent: 'center',
    flex: 1,
  },
  noCategoryFoundText: {
    color: colors.primary,
    fontFamily: fonts.medium,
    fontSize: getScaledFont(28),
  },
  noCategoryView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: height(218),
  },
  moreActionContainer: {
    width: width(180),
    padding: getScaledFont(12),
    backgroundColor: colors.toolTipBackground,
    borderRadius: width(8),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  deleteContainer: {
    flexDirection: 'row',
    borderColor: colors.secondary,
    borderBottomStartRadius: width(10),
    borderBottomEndRadius: width(10),
    borderWidth: width(2),
  },
  deleteSuccessContainer: {
    flexDirection: 'row',
    borderColor: colors.secondary,
    borderRadius: width(10),
    borderWidth: width(2),
  },
  deleteText: {
    flex: 1,
    backgroundColor: colors.secondary,
  },
  cancelText: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    height: height(76),
    marginStart: width(37),
    alignItems: 'center',
  },
  divider: {
    height: height(2),
    backgroundColor: colors.itemGrayBorder,
  },
});
