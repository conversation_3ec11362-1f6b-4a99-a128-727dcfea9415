import React, {memo} from 'react';
import {
	FlatList,
	ListRenderItemInfo,
	StyleSheet,
	Text,
	View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {fonts} from '../../../assets';
import {getScaledFont, height, width} from '../../../global/fonts';
import strings from '../../../global/strings';
import {useTheme} from '../../../global/theme';

function Allergies({data}: IAllergiesProps) {
	const {theme} = useTheme();
	function renderItem({item}: ListRenderItemInfo<IAllergy>) {
		return (
			<View style={styles.itemWrapper}>
				<View
					style={[
						styles.imageWrapper,
						{
							backgroundColor: theme?.colors.darkAppBackground,
						},
					]}>
					<FastImage
						source={{uri: item.image}}
						style={styles.image}
					/>
				</View>
				<Text
					style={[
						styles.itemName,
						{
							color: theme?.colors.darkTextColor,
						},
					]}>
					{item.name}
				</Text>
			</View>
		);
	}
	const keyExtractor = (item: IAllergy) => `${item.id}`;

	return (
		<View style={styles.wrapper}>
			<Text
				style={[
					styles.title,
					{
						color: theme?.colors.darkTextColor,
					},
				]}>
				{strings.allergics}
			</Text>
			<FlatList
				data={data}
				horizontal
				renderItem={renderItem}
				keyExtractor={keyExtractor}
				style={styles.flatlist}
				contentContainerStyle={styles.flatlistContentWrapper}
				showsHorizontalScrollIndicator={false}
			/>
		</View>
	);
}

const styles = StyleSheet.create({
	flatlist: {
		width: '100%',
	},
	flatlistContentWrapper: {
		flexGrow: 1,
		columnGap: width(19),
	},
	image: {
		aspectRatio: 1,
		height: height(24),
	},
	imageWrapper: {
		alignItems: 'center',
		aspectRatio: 1,
		// backgroundColor: colors.accentBackground,
		borderRadius: height(32) / 2,
		height: height(32),
		justifyContent: 'center',
		marginBottom: height(4),
	},
	itemName: {
		fontFamily: fonts.medium,
		// color: colors.black,
		fontSize: getScaledFont(10),
		lineHeight: getScaledFont(12.1),
		textAlign: 'center',
		textTransform: 'capitalize',
	},
	itemWrapper: {
		width: width(40),
		alignItems: 'center',
	},
	title: {
		alignSelf: 'flex-start',
		fontFamily: fonts.medium,
		// color: colors.black,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(24),
		marginBottom: height(20),
		textTransform: 'capitalize',
	},
	wrapper: {
		width: '100%',
	},
});

export default memo(Allergies);
