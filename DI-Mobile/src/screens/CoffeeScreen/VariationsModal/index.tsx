/* eslint-disable no-mixed-spaces-and-tabs */
/* eslint-disable @typescript-eslint/no-shadow */
import React, {memo, useEffect, useId, useRef, useState} from 'react';
import {
	Image,
	Modal,
	ScrollView,
	StyleSheet,
	Text,
	TouchableOpacity,
	View,
} from 'react-native';
import {fonts} from '../../../assets';
import {getScaledFont, height, width} from '../../../global/fonts';
import {useAppDispatch, useAppSelector} from '../../../store';
import {
	addOrderItem,
	addTocartGuest,
	setShowMenu,
	setUpdateOrder,
	updateOrderItem,
	updateTocartGuest,
} from '../../../store/slice/home.slice';
import Allergies from './Allergies';
import Modifer from './Modifer';
import OrderOptions from './OrderOptions';
import {useTheme} from '../../../global/theme';
import useNavigation from '../../../hooks/useNavigation';
import {useRoute} from '@react-navigation/native';

const tempAllergics: IAllergy[] = [
	{
		id: 'allergy-1',
		image: Image.resolveAssetSource(require('./allergics/egg.png')).uri,
		name: 'Egg',
	},
	{
		id: 'allergy-2',
		image: Image.resolveAssetSource(require('./allergics/gluten.png')).uri,
		name: 'Gluten',
	},
	{
		id: 'allergy-3',
		image: Image.resolveAssetSource(require('./allergics/milk.png')).uri,
		name: 'Milk',
	},
	{
		id: 'allergy-4',
		image: Image.resolveAssetSource(require('./allergics/peanuts.png')).uri,
		name: 'Nuts & Seeds',
	},
];

function VariationsModal() {
	const orderId = useId();
	const {theme} = useTheme();
	const dispatch = useAppDispatch();
	const {showMenu, isDineIn, activeGuest} = useAppSelector(
		(state: any) => state.home,
	);
	const navigation = useNavigation();
	const route = useRoute();
	const {data} = (route.params as any) || {};
	const {updateOrder} = useAppSelector(state => state.home);
	const [selectedOptions, setSelectedOptions] = useState<IOrderOption[]>([]);
	const [selectedModifiers, setSelectedModifers] = useState<IModifierItem[]>(
		[],
	);
	const optionsPrice = useRef<number>(0);
	const modifiersPrice = useRef<number>(0);
	function updateOptionsPrice(newSelectedOptions: IOrderOption[]) {
		optionsPrice.current = newSelectedOptions.reduce(
			(totalPrice, currentOption) =>
				totalPrice + (currentOption?.selectedOption?.price ?? 0),
			0,
		);
	}
	function updateModifiersPrice(selectedModifiers: IModifierItem[]): number {
		const totalPrice = selectedModifiers?.reduce((total, modifier) => {
			return total + modifier?.price;
		}, 0);
		modifiersPrice.current = totalPrice;
		return totalPrice;
	}

	useEffect(() => {
		if (data) {
			const newSelectedOptions = data?.options?.map((option: any) => {
				let selectedOption = option?.options[0];
				if (
					data?.selectedOptions &&
					data?.selectedOptions?.length > 0
				) {
					const matchingOption = data?.selectedOptions?.find(
						(selectedOpt: any) => {
							return selectedOpt?.id === option?.id;
						},
					);
					if (matchingOption) {
						selectedOption = matchingOption?.selectedOption;
					}
				}
				return {...option, selectedOption};
			});
			updateOptionsPrice(newSelectedOptions);
			setSelectedOptions(newSelectedOptions);
			setSelectedModifers(data?.selectedModifiers || []);
			updateModifiersPrice(data?.selectedModifiers);
		} else {
			setSelectedOptions([]);
		}
	}, [data]);

	function handleAdd() {
		if (!data) {
			return;
		}
		if (isDineIn) {
			dispatch(
				updateOrder?.id
					? updateTocartGuest({
							activeIndex: activeGuest,
							orderItem: {
								...data,
								selectedModifiers,
								selectedOptions,
								modifiersPrice: modifiersPrice?.current,
								optionsPrice: optionsPrice?.current,
								id: updateOrder?.id,
							},
					  })
					: addTocartGuest({
							activeIndex: activeGuest,
							orderItem: {
								...data,
								selectedModifiers,
								selectedOptions,
								modifiersPrice: modifiersPrice?.current,
								optionsPrice: optionsPrice?.current,
								id: orderId,
							},
					  }),
			);
		} else {
			dispatch(
				updateOrder?.id
					? updateOrderItem({
							...data,
							selectedModifiers,
							selectedOptions,
							modifiersPrice: modifiersPrice?.current,
							optionsPrice: optionsPrice?.current,
							id: updateOrder?.id,
					  })
					: addOrderItem({
							...data,
							selectedModifiers,
							selectedOptions,
							modifiersPrice: modifiersPrice?.current,
							optionsPrice: optionsPrice?.current,
							id: orderId,
					  }),
			);
		}
		// dispatch(setShowMenu(undefined));
		handleClose();
		dispatch(setUpdateOrder(undefined));
	}

	function handleClose() {
		// dispatch(setShowMenu(undefined));
		navigation.goBack();
	}

	function onSelectOption(optionId: number, selectedOption: IOptionSet) {
		setSelectedOptions(prevOptions => {
			const newSelectedOptions = prevOptions.map(prevOption => {
				if (prevOption.id !== optionId) {
					return prevOption;
				}
				return {...prevOption, selectedOption};
			});
			updateOptionsPrice(newSelectedOptions);
			return newSelectedOptions;
		});
	}
	function onAddModifer(modifier: IModifierItem) {
		setSelectedModifers((prevModifiers: IModifierItem[]) => {
			const isExists = prevModifiers.find(
				({name}) => name === modifier.name,
			);
			if (isExists) {
				const updatedModifiers = prevModifiers.filter(
					({name}) => name !== modifier.name,
				);
				updateModifiersPrice(updatedModifiers);
				return updatedModifiers;
			}
			const updatedModifiers = [...prevModifiers, modifier];
			updateModifiersPrice(updatedModifiers);
			return updatedModifiers;
		});
	}

	return (
		<Modal
			animationType="fade"
			transparent
			visible
			onRequestClose={handleClose}>
			<View
				style={[
					styles.wrapper,
					{
						backgroundColor: theme.colors.backDrop,
					},
				]}>
				<View
					style={[
						styles.modalView,
						{
							backgroundColor:
								theme?.colors.veryVeryLightBorderColor,
						},
					]}>
					<Text style={styles.title}>{showMenu?.item_name}</Text>
					<ScrollView
						bounces={false}
						style={styles.scrollView}
						contentContainerStyle={styles.scrollViewContent}
						showsVerticalScrollIndicator={false}>
						{showMenu?.options.map((option: IOrderOption) =>
							option.options.length ? (
								<OrderOptions
									data={option}
									selectedOption={selectedOptions.find(
										selectedOption =>
											selectedOption.id === option.id,
									)}
									onSelect={onSelectOption}
									key={`food-order-item-option-${option.id}`}
								/>
							) : null,
						)}
						{showMenu?.modifiers.map((modifier: any) =>
							modifier?.modifier_items?.length < 1 ? null : (
								<Modifer
									data={modifier}
									selectedModifiers={selectedModifiers}
									onSelect={onAddModifer}
									key={`modifier-${modifier.id}`}
								/>
							),
						)}
						<Allergies data={tempAllergics} />
					</ScrollView>
					<View style={styles.buttonsWrapper}>
						<TouchableOpacity
							style={[
								styles.button,
								{
									borderColor:
										theme?.colors.selectedButtonBackground,
								},
							]}
							onPress={handleClose}>
							<Text
								style={[
									styles.buttonText,
									{
										color: theme?.colors
											.selectedButtonBackground,
									},
								]}>
								Cancel
							</Text>
						</TouchableOpacity>
						<TouchableOpacity
							onPress={handleAdd}
							style={[
								styles.button,
								styles.addButton,
								{
									borderColor:
										theme?.colors.selectedButtonBackground,
									backgroundColor:
										theme?.colors.selectedButtonBackground,
								},
							]}>
							<Text
								style={[
									styles.buttonText,
									styles.addText,
									{
										color: theme?.colors.buttonTextColor,
									},
								]}>
								Add
							</Text>
						</TouchableOpacity>
					</View>
				</View>
			</View>
		</Modal>
	);
}

const styles = StyleSheet.create({
	addButton: {
		// backgroundColor: colors.accent,
	},
	addText: {
		// color: colors.white,
	},
	button: {
		alignItems: 'center',
		// borderColor: colors.accent,
		borderRadius: getScaledFont(4),
		borderWidth: getScaledFont(1),
		flex: 1,
		height: height(40),
		justifyContent: 'center',
	},
	buttonsWrapper: {
		flexDirection: 'row',
		gap: width(8),
		justifyContent: 'space-between',
		width: '100%',
		paddingHorizontal: height(-10),
	},
	buttonText: {
		// color: colors.accent,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(24),
		textTransform: 'capitalize',
	},
	modalView: {
		alignItems: 'center',
		alignSelf: 'center',
		// backgroundColor: colors.accentPaymentBg1,
		borderRadius: getScaledFont(12),
		height: height(650),
		justifyContent: 'center',
		paddingHorizontal: height(30),
		paddingVertical: height(24),
		width: width(416),
	},
	option: {
		alignItems: 'center',
		flex: 1,
		flexDirection: 'row',
	},
	scrollView: {
		flex: 1,
		width: '100%',
	},
	scrollViewContent: {
		flexGrow: 1,
		paddingBottom: height(12),
	},
	title: {
		fontFamily: fonts.semiBold,
		fontSize: getScaledFont(20),
		lineHeight: getScaledFont(30),
		marginBottom: height(24),
		textAlign: 'center',
		textTransform: 'capitalize',
	},
	wrapper: {
		alignItems: 'center',
		// backgroundColor: colors.backdrop,
		flex: 1,
		justifyContent: 'center',
	},
});

export default memo(VariationsModal);
