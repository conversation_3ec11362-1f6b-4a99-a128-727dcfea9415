import React, {memo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {fonts} from '../../../assets';
import {getScaledFont, height} from '../../../global/fonts';
import Radio from './Radio';
import strings from '../../../global/strings';
import {useAppSelector} from '../../../store';

function Modifier({data, selectedModifiers, onSelect}: IModiferProps) {
	const modifierItems = data?.modifier_items ?? [];
	const {activeCurrency} = useAppSelector(state => state.home);

	if (!modifierItems?.length) {
		return null;
	}

	return (
		<View style={styles.wrapper}>
			<Text style={styles.title}>{data.set_name}</Text>
			{modifierItems.map((modifier: IModifierItem) => {
				const selected = selectedModifiers.find(
					({name}) => modifier.name === name,
				)
					? true
					: false;
				return (
					<TouchableOpacity
						onPress={() => onSelect(modifier)}
						style={styles.itemsWrapper}
						key={modifier.name}>
						<View style={styles.addOnTitleWrapper}>
							<Radio selected={selected} />
							<Text style={styles.addOnTitle} numberOfLines={3}>
								{modifier.name}
							</Text>
						</View>
						<Text
							style={[styles.addOnTitle, styles.priceTxt]}
							numberOfLines={3}>
							{activeCurrency}
							{modifier.price.toFixed(2)}
						</Text>
					</TouchableOpacity>
				);
			})}
		</View>
	);
}

const styles = StyleSheet.create({
	wrapper: {
		width: '100%',
		marginBottom: height(26),
	},
	title: {
		alignSelf: 'flex-start',
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(24),
		marginBottom: height(10),
		textTransform: 'capitalize',
	},
	itemsWrapper: {
		alignItems: 'center',
		flexDirection: 'row',
		height: height(48),
		justifyContent: 'space-between',
		width: '100%',
	},
	addOns: {
		alignItems: 'center',
		flex: 1,
		flexDirection: 'row',
	},
	addOnTitleWrapper: {
		alignItems: 'center',
		flex: 1,
		flexDirection: 'row',
	},
	addOnTitle: {
		fontFamily: fonts.regular,
		fontSize: getScaledFont(14),
		// lineHeight: getScaledFont(21),
		textTransform: 'capitalize',
		width: '70%',
	},
	priceTxt: {
		textAlign: 'right',
		width: '30%',
	},
});

export default memo(Modifier);
