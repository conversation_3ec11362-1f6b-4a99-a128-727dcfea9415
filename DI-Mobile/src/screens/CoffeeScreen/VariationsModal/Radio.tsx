import React, {memo} from 'react';
import {StyleSheet, View} from 'react-native';
import CheckedBox from '../../../assets/icons/modifier_Checked.svg';
import UnCheckedBox from '../../../assets/icons/modifier_Unchecked.svg';
import SelectedRadio from '../../../assets/icons/radio_Selected.svg';
import UnSelectedRadio from '../../../assets/icons/radio_Unselected.svg';
import {getScaledFont} from '../../../global/fonts';

function Radio({selected, round}: {selected: boolean; round?: boolean}) {
	return (
		<View>
			{round ? (
				selected ? (
					<SelectedRadio style={styles.button} />
				) : (
					<UnSelectedRadio style={styles.button} />
				)
			) : selected ? (
				<CheckedBox style={styles.button1} />
			) : (
				<UnCheckedBox style={styles.button1} />
			)}
		</View>
	);
}

const styles = StyleSheet.create({
	button: {
		height: getScaledFont(24),
		aspectRatio: 1,
		marginEnd: getScaledFont(10),
	},
	button1: {
		height: getScaledFont(21),
		aspectRatio: 1,
		marginEnd: getScaledFont(10),
	},
});

export default memo(Radio);
