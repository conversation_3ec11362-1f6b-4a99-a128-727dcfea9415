import React, {memo} from 'react';
import {
	ScrollView,
	StyleSheet,
	Text,
	TouchableOpacity,
	View,
} from 'react-native';
import {fonts} from '../../../assets';
import {getScaledFont, height, width} from '../../../global/fonts';
import {useTheme} from '../../../global/theme';
import {useAppSelector} from '../../../store';
import Radio from './Radio';

function OrderSet({data, selectedOption, onSelect}: IOrderSetProps) {
	const hasPrice = data.options.some(optionSet => !!optionSet.price);
	const {theme} = useTheme();
	const {activeCurrency} = useAppSelector(state => state.home);

	if (hasPrice) {
		return (
			<View style={styles.wrapper}>
				<Text style={styles.heading}>{data.option_set_name}</Text>
				<View style={styles.setsWrapper}>
					{data.options.map((optionSet: IOptionSet) => {
						const selected =
							optionSet.option_name ===
							selectedOption?.selectedOption?.option_name;
						return (
							<TouchableOpacity
								onPress={() => onSelect(data.id, optionSet)}
								style={styles.sizeOption}
								key={`${data.id}-${optionSet.option_name}`}>
								<View style={styles.sizeTitleWrapper}>
									<Radio selected={selected} round />
									<Text
										style={[
											styles.sizeTitle,
											{
												color: theme?.colors
													.darkTextColor,
											},
										]}>
										{optionSet.option_name}
									</Text>
								</View>
								<Text
									style={[
										styles.sizeTitle,
										styles.priceTxt,
										{
											color: theme?.colors.darkTextColor,
										},
									]}>
									{activeCurrency}
									{(optionSet?.price ?? 0).toFixed(2)}
								</Text>
							</TouchableOpacity>
						);
					})}
				</View>
			</View>
		);
	}

	return (
		<View style={styles.wrapper}>
			<Text style={styles.heading}>{data.option_set_name}</Text>
			<ScrollView
				horizontal
				bounces={false}
				style={styles.setsWrapper}
				contentContainerStyle={styles.scrollViewContent}
				showsHorizontalScrollIndicator={false}>
				{data.options.map((optionSet: IOptionSet) => {
					const selected =
						optionSet.option_name ===
						selectedOption?.selectedOption?.option_name;
					return (
						<TouchableOpacity
							onPress={() => onSelect(data.id, optionSet)}
							style={styles.option}
							key={`${data.id}-${optionSet.option_name}`}>
							<Radio selected={selected} round />
							<Text style={styles.addOnTitle}>
								{optionSet.option_name}
							</Text>
						</TouchableOpacity>
					);
				})}
			</ScrollView>
		</View>
	);
}

const styles = StyleSheet.create({
	wrapper: {
		width: '100%',
		marginBottom: height(26),
	},
	addOnTitle: {
		fontFamily: fonts.regular,
		fontSize: getScaledFont(14),
		lineHeight: getScaledFont(21),
		textTransform: 'capitalize',
	},
	heading: {
		alignSelf: 'flex-start',
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(24),
		marginBottom: height(10),
		textTransform: 'capitalize',
	},
	option: {
		alignItems: 'center',
		flex: 1,
		flexDirection: 'row',
		marginRight: width(16),
	},
	sizeOption: {
		alignItems: 'center',
		flexDirection: 'row',
		height: height(44),
		justifyContent: 'space-between',
		width: '100%',
	},
	priceTxt: {
		width: '30%',
		textAlign: 'right',
	},
	sizeTitle: {
		// color: colors.black,
		fontFamily: fonts.regular,
		fontSize: getScaledFont(14),
		// lineHeight: getScaledFont(21),
		textTransform: 'capitalize',
		width: '70%',
	},
	sizeTitleWrapper: {
		alignItems: 'center',
		flex: 1,
		flexDirection: 'row',
		width: '100%',
	},
	setsWrapper: {
		width: '100%',
	},
	scrollViewContent: {flexGrow: 1},
});

export default memo(OrderSet);
