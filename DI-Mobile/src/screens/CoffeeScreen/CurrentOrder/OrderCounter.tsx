import React, { memo } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { fonts } from '../../../assets';
import { getScaledFont, height, width } from '../../../global/fonts';
import { useTheme } from '../../../global/theme';

import { useAppSelector } from '../../../store';

function OrderCounter(props: IOrderCounterProps) {
	const { theme } = useTheme();
	const { isPaymentMode } = useAppSelector((state: any) => state.home);
	const { showAmountSplit } = useAppSelector((state: any) => state.amountSplit);
	const disable = isPaymentMode || showAmountSplit ? 0.6 : 1;
	return (
		<View style={styles.wrapper}>
			{/* <Pressable
				onPress={props.onDecrease}
				style={[
					styles.item,
					styles.decreaseButton,
					{opacity: disable},
					{
						backgroundColor: theme?.colors.selectedButtonBackground,
					},
				]}>
				<Text
					style={[
						styles.buttonText,
						{
							color: theme?.colors.enableButtonTextColor,
						},
					]}>
					-
				</Text>
			</Pressable> */}
			<View style={styles.item}>
				<Text style={styles.value}>{props.value}</Text>
			</View>
			{/* <Pressable
				onPress={props.onIncrease}
				style={[
					styles.item,
					styles.increaseButton,
					{opacity: disable},
					{
						backgroundColor: theme?.colors.selectedButtonBackground,
					},
				]}>
				<Text
					style={[
						styles.buttonText,
						{
							color: theme?.colors.enableButtonTextColor,
						},
						{opacity: disable},
					]}>
					+
				</Text>
			</Pressable> */}
		</View>
	);
}

const styles = StyleSheet.create({
	wrapper: {
		width: width(100),
		height: height(32),
		flexDirection: 'row',
		alignItems: 'center',
	},
	item: {
		height: height(32),
		aspectRatio: 1,
		alignItems: 'center',
		justifyContent: 'center',
	},
	decreaseButton: {
		borderTopLeftRadius: getScaledFont(4),
		borderBottomLeftRadius: getScaledFont(4),
		// backgroundColor: colors.accent,
	},
	increaseButton: {
		borderTopRightRadius: getScaledFont(4),
		borderBottomRightRadius: getScaledFont(4),
		// backgroundColor: colors.accent,
	},
	buttonText: {
		// color: colors.white,
		fontFamily: fonts.medium,
		fontWeight: '500',
		fontSize: getScaledFont(12),
		lineHeight: getScaledFont(18),
	},
	value: {},
});

export default memo(OrderCounter);
