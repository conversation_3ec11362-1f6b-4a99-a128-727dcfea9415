/* eslint-disable react-native/no-inline-styles */
import React, {memo} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {fonts} from '../../../assets';
import colors from '../../../global/colors';
import {getScaledFont, height, width} from '../../../global/fonts';
import strings from '../../../global/strings';
import {useTheme} from '../../../global/theme';
import {calculateOrderPrice} from '../../../global/utilities';
import {useAppDispatch, useAppSelector} from '../../../store';
import {
	addOrderItem,
	addTocartGuest,
	removeFromCartGuest,
	removeOrderItem,
	setCoffeeUpdate,
	setExpandedItem,
	setShowMenu,
	setUpdateOrder,
} from '../../../store/slice/home.slice';
import useNavigation from '../../../hooks/useNavigation';
import screenNames from '../../../global/screenNames';

function OrderItem(props: IOrderItemProps) {
	const {data, style, guestIndex, itemIndex, itemlength} = props;
	const {theme} = useTheme();
	const navigation = useNavigation();
	const dispatch = useAppDispatch();
	const {isDineIn, isPaymentMode, activeCurrency} = useAppSelector(
		(state: any) => state.home,
	);
	const {showAmountSplit} = useAppSelector((state: any) => state.amountSplit);
	const modifiers = data?.selectedModifiers?.map(modifier => modifier?.name);
	const selectedOptions: any = data?.selectedOptions?.map(
		option => option?.selectedOption?.option_name,
	);
	const combinedData = modifiers?.concat(selectedOptions);
	function onDecrease() {
		if (!isPaymentMode && !showAmountSplit) {
			if (isDineIn) {
				dispatch(
					removeFromCartGuest({
						activeIndex: guestIndex,
						orderItem: data,
					}),
				);
				return;
			}
			dispatch(removeOrderItem(data));
		}
	}
	function onIncrease() {
		if (!isPaymentMode && !showAmountSplit) {
			if (isDineIn) {
				dispatch(
					addTocartGuest({activeIndex: guestIndex, orderItem: data}),
				);
				return;
			}
			dispatch(addOrderItem(data));
		}
	}

	function handleCustomize() {
		if (!isPaymentMode && !showAmountSplit) {
			const showMenu =
				data?.options?.length > 0 || data?.modifiers?.length > 0;
			if (showMenu) {
				dispatch(setUpdateOrder({id: data?.itemId}));
				// dispatch(setShowMenu(data));
				navigation.navigate(screenNames.VariationsModal, {data: data});
				return;
			}
		}
	}
	function handleModify() {
		dispatch(setExpandedItem(data?.id));
		dispatch(
			setCoffeeUpdate({id: data?.itemId, itemData: data?.modifiers}),
		);
	}

	function handleCircleView(item: any) {
		const color = '#fbf1e8';
		const bordercolor = '#f2c8ae';
		const textColor = colors.accent500;
		return (
			<View
				style={[
					styles.itemBtn,
					{
						backgroundColor: color,
						borderColor: bordercolor,
					},
				]}>
				<Text style={[styles.innerTxt, {color: textColor}]}>
					{item}
				</Text>
			</View>
		);
	}

	const isModifierValueNotEmpty = (data: any, type: any) => {
		const value = data?.modifiers?.[type]?.value;
		return (
			(Array.isArray(value) &&
				value?.length > 0 &&
				value?.some(item => item !== '')) ||
			(!!value && typeof value === 'string')
		);
	};

	return (
		<>
			<View style={[styles.wrapper, style]}>
				<Image
					style={[
						styles.image,
						{
							backgroundColor:
								theme?.colors.unSelectedButtonTextColor,
						},
					]}
					source={{uri: data?.image_url}}
				/>
				<View style={styles.detailsWrapper}>
					<Text
						style={[
							styles.title,
							{
								color: theme?.colors.textBlack,
							},
						]}
						numberOfLines={1}>
						{data?.item_name}
					</Text>
					<View style={styles.modifyView}>
						{isModifierValueNotEmpty(data, 'Sugar') ? (
							<View style={styles.middleView}>
								<Text
									style={[
										styles.description,
										{
											color: theme?.colors.textBlack,
										},
									]}
									numberOfLines={1}>
									{'Sugar :'}
								</Text>
								{handleCircleView(
									data?.modifiers?.Sugar?.value,
								)}
							</View>
						) : (
							<View style={styles.emptyView} />
						)}
						{isModifierValueNotEmpty(data, 'Ice') ? (
							<View style={styles.middleView}>
								<Text
									style={[
										styles.description,
										{
											color: theme?.colors.textBlack,
										},
									]}
									numberOfLines={1}>
									{'Ice :'}
								</Text>
								{handleCircleView(data?.modifiers?.Ice?.value)}
							</View>
						) : (
							<View style={styles.emptyView} />
						)}
					</View>
					<View style={styles.priceWrapper}>
						<View style={{flexDirection: 'row'}}>
							<Text
								style={[
									styles.price,
									{
										color: theme?.colors.textBlack,
									},
								]}>
								{`${'x '}${data?.orderQuantity}`}
							</Text>
							<Text style={[styles.noteTxt]}>{'Note'}</Text>
							<Text
								style={[styles.noteTxt]}
								onPress={handleModify}>
								{'Modify'}
							</Text>
						</View>
						<Text
							style={[
								styles.price,
								{
									color: theme?.colors.textBlack,
								},
							]}>
							{`${'x '}${data?.orderQuantity}`}
						</Text>
						<Text style={[styles.noteTxt]}>{'Note'}</Text>
						<Text
							style={[
								styles.price,
								{
									color: '#a5a6a9',
								},
							]}>
							{activeCurrency}
							{calculateOrderPrice(data)?.toFixed(2)}
						</Text>
					</View>
				</View>
			</View>
			{combinedData && combinedData?.length > 0 ? (
				<View style={styles.mainListView}>
					<View style={styles.listStyle}>
						{combinedData.map((item, index: any) => {
							const value =
								combinedData &&
								combinedData?.length - 1 === index
									? '.'
									: ',';
							return (
								<View style={styles.nameSize}>
									<Text
										style={[
											styles?.title,
											{
												color: theme?.colors
													.unSelectedButtonTextColor,
											},
										]}>{`${item}${value}`}</Text>
								</View>
							);
						})}
					</View>
					<TouchableOpacity
						style={styles.customView}
						onPress={handleCustomize}>
						<Text
							style={[
								styles.customTxt,
								{
									color: theme?.colors
										.disableButtonBackgroundColor,
								},
							]}>
							{strings.custom}
						</Text>
					</TouchableOpacity>
				</View>
			) : null}
			{itemlength - 1 === itemIndex ? null : (
				<View
					style={[
						styles.bottomLine,
						{
							borderColor: theme?.colors.tabBackgroundColor,
						},
					]}
				/>
			)}
		</>
	);
}

const styles = StyleSheet.create({
	bottomLine: {
		alignSelf: 'center',
		// borderColor: colors.tabsBg,
		borderWidth: 1,
		width: '100%',
	},
	description: {
		// color: colors.primary,
		fontFamily: fonts.regular,
		fontWeight: '400',
		fontSize: getScaledFont(12),
		lineHeight: getScaledFont(18),
		textTransform: 'capitalize',
	},
	detailsWrapper: {
		flex: 1,
	},
	image: {
		aspectRatio: 1,
		// backgroundColor: colors.primary,
		borderRadius: getScaledFont(8),
		height: height(100),
		marginRight: width(12),
		alignSelf: 'center',
	},
	modifyView: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'flex-start',
	},
	itemBtn: {
		height: getScaledFont(30),
		width: getScaledFont(30),
		borderRadius: getScaledFont(25),
		borderWidth: getScaledFont(1),
		alignItems: 'center',
		justifyContent: 'center',
		marginHorizontal: width(5),
		marginVertical: height(10),
	},
	innerTxt: {
		color: colors.black,
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		fontSize: getScaledFont(12),
	},
	noteWrapper: {
		width: width(100),
		height: height(30),
		alignItems: 'center',
		justifyContent: 'center',
		borderRadius: getScaledFont(8),
	},
	middleView: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'center',
	},
	emptyView: {
		marginTop: height(40),
	},
	customView: {
		marginHorizontal: getScaledFont(10),
		alignSelf: 'center',
	},
	mainListView: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		marginBottom: height(10),
		marginTop: height(-4),
	},
	customTxt: {
		// color: colors.orange,
		fontFamily: fonts.medium,
		fontWeight: '500',
		fontSize: getScaledFont(18),
		lineHeight: getScaledFont(27),
		textDecorationLine: 'underline',
	},
	listStyle: {
		flexDirection: 'row',
		flexWrap: 'wrap',
		flex: 1,
	},
	nameSize: {
		marginHorizontal: width(3),
	},
	listContainer: {
		flexDirection: 'row',
		flexWrap: 'wrap',
	},
	price: {
		// color: colors.primary,
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		fontSize: getScaledFont(18),
		lineHeight: getScaledFont(27),
	},
	noteTxt: {
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		fontSize: getScaledFont(14),
		lineHeight: getScaledFont(27),
		backgroundColor: '#f5efef',
		color: '#b29991',
		paddingHorizontal: getScaledFont(20),
		borderRadius: getScaledFont(6),
		overflow: 'hidden',
		marginLeft: 10,
	},
	priceWrapper: {
		alignItems: 'center',
		flexDirection: 'row',
		justifyContent: 'space-between',
		width: '100%',
		overflow: 'hidden',
	},
	title: {
		// color: colors.primary,
		fontFamily: fonts.medium,
		fontWeight: '500',
		fontSize: getScaledFont(14),
		// lineHeight: getScaledFont(21),
		textTransform: 'capitalize',
	},
	titleHeader: {
		// color: colors.orange,
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		fontSize: getScaledFont(14),
		lineHeight: getScaledFont(21),
		textTransform: 'uppercase',
		paddingHorizontal: width(10),
	},
	wrapper: {
		alignItems: 'flex-start',
		flexDirection: 'row',
		height: 'auto',
		width: '100%',
	},
});

export default memo(OrderItem);
