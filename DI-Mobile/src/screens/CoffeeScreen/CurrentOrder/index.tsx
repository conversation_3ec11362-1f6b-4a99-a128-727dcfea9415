/* eslint-disable react-native/no-inline-styles */
import React, {memo} from 'react';
import {
	FlatList,
	ListRenderItemInfo,
	Platform,
	StyleSheet,
	Text,
	TouchableOpacity,
	View,
} from 'react-native';
import Animated from 'react-native-reanimated';
import Tooltip from 'react-native-walkthrough-tooltip';
import {fonts} from '../../../assets';
import Movevert from '../../../assets/icons/guestBooking/more_vert.svg';
import ToolBack from '../../../assets/icons/toolBack.svg';
import {Button} from '../../../components/button';
import Label from '../../../components/label';
import GuestCart from '../../../components/payment/GuestCart';
import {checkCartHasItems, dineInMore} from '../../../global/constants';
import {getScaledFont, height, width} from '../../../global/fonts';
import strings from '../../../global/strings';
import {useAppDispatch, useAppSelector} from '../../../store';
import {setActiveGuest, showMoreOptions} from '../../../store/slice/home.slice';
import PaymentDetails from '../PaymentDetails';
import {useTheme} from '../../../global/theme';
import OrderItem from '../../HomeScreen/CurrentOrder/OrderItem';

const More = memo(({data, show, onClose}: any) => {
	let hasItemsInCart = checkCartHasItems();
	const {openedOrderDetails} = useAppSelector(state => state.order);
	const {theme} = useTheme();
	return (
		<Tooltip
			isVisible={show}
			topAdjustment={Platform.OS === 'android' ? -23 : 0}
			onClose={onClose}
			content={
				<FlatList
					data={data}
					style={[
						styles.dataWrapper,
						{
							borderColor: theme?.colors.selectedButtonBackground,
						},
					]}
					scrollEnabled={false}
					renderItem={({item}: ListRenderItemInfo<IMoree>) => {
						return (
							<Button
								disabled={
									item.label === strings.addOrderDiscount
										? openedOrderDetails?.id
											? false
											: true
										: false
								}
								labelStyle={styles.lableStyle}
								style={[
									styles.guestPopOverBtnStyles,
									{
										backgroundColor:
											theme?.colors.transparent,
										borderBottomColor:
											theme?.colors
												.selectedButtonBackground,
									},
								]}
								text={item.label}
								fontColor={theme?.colors.textBlack}
								onPress={() => {
									onClose();
									item?.action?.();
								}}
							/>
						);
					}}
				/>
			}
			placement={'bottom'}
			arrowSize={{
				height: height(24),
				width: height(30),
			}}
			arrowStyle={{
				left: width(28),
				zIndex: 1,
			}}
			displayInsets={{
				left: width(355),
			}}
			contentStyle={styles.popupContainer}>
			<TouchableOpacity onPress={onClose}>
				{show ? (
					<ToolBack style={styles.openIcon} />
				) : (
					<Movevert
						fill={theme?.colors.darkTextColor}
						style={styles.Moveverticon}
					/>
				)}
			</TouchableOpacity>
		</Tooltip>
	);
});

function CurrentOrder() {
	const {theme} = useTheme();
	const {orderItems, isDineIn, showMoreOptionsForDineIN, isPaymentMode} =
		useAppSelector((state: any) => state.home);
	const dispatch = useAppDispatch();
	const moreData = dineInMore(dispatch);

	function renderItem({item}: ListRenderItemInfo<IOrderItem>) {
		return <OrderItem data={item} style={styles.wrapperTop} />;
	}

	const keyExtractor = (item: IOrderItem) => `${item?.id}`;

	return (
		<View
			style={[
				styles.wrapper,
				{
					backgroundColor: theme?.colors.enableButtonTextColor,
				},
			]}>
			<View style={styles.titleWrapper}>
				{/* <Label
					style={[
						styles.title,
						{
							color: theme?.colors.enableButtonTextColor,
						},
					]}
					text={'Bills'}
					// showUnderLine={true}
					labelStyle={styles.lineTop}
				/> */}
				<Text
					style={[
						styles.title,
						{
							color: theme?.colors.textInputFontColor,
						},
					]}>
					{'Bills'}
				</Text>
				{isDineIn ? (
					<More
						data={moreData}
						show={showMoreOptionsForDineIN}
						onClose={() => {
							!isPaymentMode &&
								dispatch(
									showMoreOptions(!showMoreOptionsForDineIN),
								);
							dispatch(setActiveGuest(0));
						}}
					/>
				) : null}
			</View>
			{isDineIn ? (
				<GuestCart />
			) : (
				<Animated.FlatList
					data={orderItems}
					renderItem={renderItem}
					keyExtractor={keyExtractor}
					style={styles.flatlist}
					contentContainerStyle={styles.flatlistContent}
					showsVerticalScrollIndicator={false}
				/>
			)}
			<PaymentDetails />
		</View>
	);
}

const styles = StyleSheet.create({
	lableStyle: {
		marginStart: width(24),
	},
	wrapperTop: {
		marginVertical: getScaledFont(10),
	},
	dataWrapper: {
		flex: 1,
		// borderColor: colors.accent,
		borderWidth: 1,
		borderRadius: getScaledFont(8),
	},
	popupContainer: {
		padding: 0,
		margin: 0,
		borderRadius: getScaledFont(8),
		marginStart: width(-20),
	},
	openIcon: {
		left: width(20),
	},
	guestPopOverBtnStyles: {
		// backgroundColor: colors.transparent,
		borderBottomWidth: 1,
		// borderBottomColor: colors.accent,
		padding: 0,
		marginBottom: 0,
		width: width(260),
		height: height(56),
		borderRadius: width(0),
		justifyContent: 'flex-start',
	},
	flexCol: {
		flexDirection: 'column',
	},
	lineTop: {
		marginTop: getScaledFont(2),
	},
	flatlist: {
		flex: 1,
	},
	flatlistContent: {
		flexGrow: 1,
		paddingVertical: height(20),
	},
	title: {
		// color: colors.primary,
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		fontSize: getScaledFont(24),
		lineHeight: getScaledFont(27),
	},
	titleBorder: {
		// borderColor: colors.accent,
		borderRadius: getScaledFont(2),
		borderWidth: getScaledFont(2),
	},
	circleView: {
		// backgroundColor: colors.white,
		alignItems: 'center',
		justifyContent: 'center',
		height: 50,
		width: 50,
		borderRadius: 50 / 2,
	},
	titleWrapper: {
		alignSelf: 'flex-start',
		alignItems: 'center',
		justifyContent: 'space-between',
		flexDirection: 'row',
		width: '96%',
		height: height(40),
	},
	wrapper: {
		// backgroundColor: colors.white,
		borderRadius: getScaledFont(8),
		flex: 1,
		paddingBottom: height(12),
		paddingHorizontal: width(20),
		paddingTop: height(20),
		width: width(400),
	},
	Moveverticon: {
		width: 36,
		height: 36,
		marginRight: 10,
	},
});

export default memo(CurrentOrder);
