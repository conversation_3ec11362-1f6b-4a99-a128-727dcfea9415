/* eslint-disable react-hooks/exhaustive-deps */
import React, {memo, useEffect, useMemo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {fonts} from '../../assets';
import {getScaledFont, height, width} from '../../global/fonts';
import strings from '../../global/strings';
import {calculateSubtotal} from '../../global/utilities';
import {useAppDispatch, useAppSelector} from '../../store';
import {
	paymentData,
	setDiscountModal,
	setDiscountPrice,
	setRemainingBill,
	setTotalBill,
} from '../../store/slice/home.slice';
import {fireItOff, update_fireItOff} from '../../store/thunk/home.thunk';
import {setIsPaymentMode} from '../../store/slice/home.slice';
import {useTheme} from '../../global/theme';
import {setShowAmountSplit} from '../../store/slice/amountSplit.slice';

function PaymentDetails() {
	const {
		orderItems,
		isDineIn,
		dineIn,
		dineInDetails,
		discountPrice,
		isPercentage,
		isPaymentMode,
		isRemaining,
		isModify,
		activeCurrency,
		discountPercentage,
	} = useAppSelector(state => state.home);
	const {showAmountSplit} = useAppSelector(state => state.amountSplit);
	const {openedOrderDetails} = useAppSelector(state => state.order);
	const {activeStaffData} = useAppSelector(state => state.user);
	const dispatch = useAppDispatch();
	const subTotal = calculateSubtotal(orderItems, dineIn, isDineIn);
	const discount = useMemo(
		() =>
			subTotal > 0
				? isPercentage
					? (discountPrice / 100) * subTotal
					: Number(discountPrice)
				: 0,
		[discountPrice, subTotal, isPercentage],
	);
	var tax = subTotal * 0.1;
	const total = subTotal + tax - discount;

	const disable = useMemo(() => total <= 0, [subTotal]);
	const {theme} = useTheme();

	useEffect(() => {
		if (subTotal === 0) {
			dispatch(
				setDiscountPrice({
					price: Number(0),
					isPercentage: true,
					percentage: Number(0)
				}),
			);
		}
	}, [disable]);
	function FireItOff() {
		const floorID = dineInDetails?.id ? dineInDetails?.id : 0;
		const DineCheckoutJSON = {
			order_type: 'Dine-In',
			table_id: floorID,
			total_price: total,
			split_payment: false,
			notes: 'Extra',
			discount: parseFloat(discount.toFixed(2)),
			tax: parseFloat(tax.toFixed(2)),
			sub_total: parseFloat(subTotal.toFixed(2)),
			orderItems: dineIn,
			employee: activeStaffData?.results?.employee_name,
		};
		const openOrderCheckoutJSON = {
			order_type: 'To-Go',
			total_price: total,
			split_payment: false,
			notes: 'Extra',
			discount: parseFloat(discount.toFixed(2)),
			tax: parseFloat(tax.toFixed(2)),
			sub_total: parseFloat(subTotal.toFixed(2)),
			orderItems: orderItems,
			employee: activeStaffData?.results?.employee_name,
		};
		const orderItemsSet = new Set([
			...(DineCheckoutJSON?.orderItems ?? []),
		]);
		const orderItemsSetTakeaway = new Set([
			...(openOrderCheckoutJSON?.orderItems ?? []),
		]);
		if (openedOrderDetails?.id) {
			const JSON = {
				...openOrderCheckoutJSON,
				orderItems: [...orderItemsSet],
				orderID: openedOrderDetails?.id,
			};
			const TakeAwayJSON = {
				...openOrderCheckoutJSON,
				orderItems: [...orderItemsSetTakeaway],
				orderID: openedOrderDetails?.id,
			};
			dispatch(
				update_fireItOff(
					openedOrderDetails?.order_type === 'To-Go' ||
						openedOrderDetails?.order_type === 'To Go'
						? TakeAwayJSON
						: JSON,
				),
			);
			return;
		}
		const JSON = isDineIn ? DineCheckoutJSON : openOrderCheckoutJSON;
		dispatch(fireItOff(JSON));
	}
	useEffect(() => {
		//Saving Payment Data
		dispatch(
			paymentData({
				total,
				subTotal,
				tax,
				discount,
				isPercentage,
				discountPrice,
				discountPercentage,
			}),
		);
		if (!isRemaining) {
			dispatch(setTotalBill(Number(total).toFixed(2)));
		}
	}, [total, isRemaining]);
	const disableMode = isModify ? 0.3 : 1;

	return (
		<View
			style={[
				styles.wrapper,
				{
					backgroundColor: theme?.colors.veryVeryLightBorderColor,
				},
			]}>
			<View style={styles.row}>
				<Text
					style={[
						styles.priceTitle,
						{
							color: theme?.colors.unSelectedButtonTextColor,
						},
					]}>
					{strings.subTotal}
				</Text>
				<Text
					style={[
						styles.price,
						{
							color: theme?.colors.mediumDarkBorderColor,
						},
					]}>
					{activeCurrency}
					{subTotal.toFixed(2)}
				</Text>
			</View>
			<View style={styles.row}>
				<Text
					style={[
						styles.priceTitle,
						{
							color: theme?.colors.unSelectedButtonTextColor,
						},
					]}>{`${strings.discount} ${
					isPercentage ? `(${discountPrice.toFixed(2)}%)` : ''
				}`}</Text>
				<Text
					style={[
						styles.price,
						{
							color: theme?.colors.mediumDarkBorderColor,
						},
					]}>
					{activeCurrency}
					{discount.toFixed(2)}
				</Text>
			</View>
			{/* <View style={[styles.row, styles.noMarginBottom]}>
				<Text
					style={[
						styles.priceTitle,
						{
							color: theme?.colors.unSelectedButtonTextColor,
						},
					]}>
					{strings.tax + '(10%)'}
				</Text>
				<Text
					style={[
						styles.price,
						{
							color: theme?.colors.mediumDarkBorderColor,
						},
					]}>
					{activeCurrency}
					{tax.toFixed(2)}
				</Text>
			</View> */}
			<View
				style={[
					styles.dash,
					{
						borderColor: theme?.colors.textInputBorderColor,
					},
				]}
			/>
			<View style={[styles.row, styles.noMarginBottom]}>
				<Text
					style={[
						styles.priceTitle,
						styles.total,
						{
							color: theme?.colors.unSelectedButtonTextColor,
						},
					]}>
					{strings.total}
				</Text>
				<Text
					style={[
						styles.price,
						styles.total,
						{
							color: theme?.colors.mediumDarkBorderColor,
						},
					]}>
					{activeCurrency}
					{total.toFixed(2)}
				</Text>
			</View>
			{!isPaymentMode && !showAmountSplit ? (
				<>
					{/* <TouchableOpacity
						disabled={disable}
						onPress={() => {
							dispatch(setDiscountModal(true));
						}}
						style={[
							styles.button,
							styles.discountButton,
							{
								borderColor:
									theme?.colors.selectedButtonBackground,
							},
						]}>
						<Text
							style={[
								styles.buttonText,
								{
									color: disable
										? `${theme?.colors.selectedButtonBackground}90`
										: theme?.colors
												.selectedButtonBackground,
								},
							]}>
							{strings.discount}
						</Text>
					</TouchableOpacity> */}

					<TouchableOpacity
						disabled={disable}
						onPress={FireItOff}
						style={[
							styles.button,
							styles.fireButton,
							{
								backgroundColor: disable
									? `${theme?.colors.selectedButtonBackground}90`
									: theme?.colors.selectedButtonBackground,
							},
						]}>
						<Text
							style={[
								styles.buttonText,
								styles.fireButtonText,
								{
									// color: theme?.colors
									// 	.selectedButtonBackground,

									color: theme?.colors.enableButtonTextColor,
								},
							]}>
							{isDineIn ? strings.fireItOff : 'CheckOut'}
						</Text>
					</TouchableOpacity>
				</>
			) : (
				<TouchableOpacity
					disabled={disable || isModify}
					onPress={() => {
						if (!isModify) {
							dispatch(setShowAmountSplit(false));
							dispatch(setIsPaymentMode(false));
							dispatch(setRemainingBill(false));
						}
					}}
					style={[
						styles.button,
						styles.fireButton,
						{
							backgroundColor:
								theme?.colors.selectedButtonBackground,
							marginTop: height(48),
						},
					]}>
					<Text
						style={[
							styles.buttonText,
							styles.fireButtonText,
							{
								// color: theme?.colors.selectedButtonBackground,
								color: theme?.colors.enableButtonTextColor,
							},
							{opacity: disableMode},
						]}>
						{strings.modifyItems}
					</Text>
				</TouchableOpacity>
			)}
		</View>
	);
}

const styles = StyleSheet.create({
	row: {
		width: '100%',
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		marginBottom: height(8),
	},
	noMarginBottom: {
		marginBottom: 0,
	},
	wrapper: {
		width: '100%',
		height: 'auto',
		paddingTop: height(38),
		paddingBottom: height(16),
		paddingHorizontal: width(20),
		// backgroundColor: colors.accentPaymentBg1,
	},
	priceTitle: {
		fontSize: getScaledFont(12),
		lineHeight: getScaledFont(18),
		fontFamily: fonts.medium,
		fontWeight:'500',
		// color: colors.primary,
		textTransform: 'capitalize',
	},
	price: {
		fontSize: getScaledFont(12),
		lineHeight: getScaledFont(18),
		fontFamily: fonts.medium,
		fontWeight:'500',
		// color: colors.priceGrey,
	},
	total: {
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(24),
	},
	dash: {
		marginVertical: height(24),
		alignSelf: 'center',
		width: '100%',
		borderWidth: 1,
		borderStyle: 'dotted',
	},
	button: {
		width: '100%',
		height: height(60),
		alignItems: 'center',
		justifyContent: 'center',
		borderRadius: height(30),
	},
	discountButton: {
		borderWidth: getScaledFont(1),
		// borderColor: colors.accent,
		marginTop: height(20),
	},
	fireButton: {
		marginTop: height(30),
	},
	buttonText: {
		fontSize: getScaledFont(16),
		lineHeight: getScaledFont(24),
		// color: colors.accent,
		textAlign: 'center',
	},
	fireButtonText: {
		// color: colors.white,
	},
});

export default memo(PaymentDetails);
