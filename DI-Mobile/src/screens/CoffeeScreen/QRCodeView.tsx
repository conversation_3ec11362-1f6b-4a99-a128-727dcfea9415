import React, {memo, useEffect, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {fonts} from '../../assets';
import {getScaledFont, height, width} from '../../global/fonts';
import strings from '../../global/strings';
import {useAppDispatch, useAppSelector} from '../../store';
import QRCode from 'react-native-qrcode-svg';
import {QR_URL} from '@env';
import useNavigation from '../../hooks/useNavigation';
import {
	clearDineIn,
	setActiveTab,
	setIsPaymentMode,
	setRemainingBill,
	setTotalBill,
	setmodify,
} from '../../store/slice/home.slice';
import {clearPaymentDetails} from '../../store/slice/vipService.slice';
import screenNames from '../../global/screenNames';
import {getAuthHeaders} from '../../global/utilities';
import {endpoints, responseCodes} from '../../services/constants';
import Axios from '../../services/api';
import axios from 'axios';
import {Button} from '../../components/button';
import {setActiveOpenedOrder} from '../../store/slice/orders.slice';
import {useTheme} from '../../global/theme';
import {setShowAmountSplit} from '../../store/slice/amountSplit.slice';
import {useWebSocket} from '../../services/WebsocketProvider';

function QRCodeView() {
	const {theme} = useTheme();
	const {QRCodeData, activeCurrency} = useAppSelector(state => state.home);
	const url = `${QR_URL}payment?orderId=${QRCodeData.id}&amount=${parseFloat(
		QRCodeData.amount,
	).toFixed(2)}&orderType=${QRCodeData.type}&currency=${activeCurrency}`;
	const navigation = useNavigation();
	const dispatch = useAppDispatch();
	const [paymentState, setPaymentState] = useState('');
	const {orderDetails, resetOrderDetails} = useWebSocket();

	// useEffect(() => {
	// 	const intervalId = setInterval(() => {
	// 		if (
	// 			paymentState.toLowerCase() === strings.paid ||
	// 			paymentState.toLowerCase() === 'done'
	// 		) {
	// 			dispatch(setIsPaymentMode(false));
	// 			dispatch(clearDineIn());
	// 			setPaymentState('');
	// 			dispatch(clearPaymentDetails());
	// 			navigation.navigate(screenNames.paymentSuccess, {
	// 				amount: parseFloat(QRCodeData?.amount ?? 0).toFixed(2),
	// 				id: QRCodeData.id,
	// 				remainingAmount: 0,
	// 			});
	// 			clearInterval(intervalId);
	// 		} else {
	// 			if (QRCodeData.type === 'dineIn') {
	// 				getDineStatus();
	// 			} else if (QRCodeData.type === 'VIPSingle') {
	// 				getSingleStatus();
	// 			} else if (QRCodeData.type === 'VIPBulk') {
	// 				getBulkStatus();
	// 			}
	// 		}
	// 	}, 10000);
	// 	return () => clearInterval(intervalId);
	// }, [QRCodeData.id, QRCodeData.type, paymentState]);

	useEffect(() => {
		if (
			orderDetails?.status.toLowerCase() === strings.paid ||
			orderDetails?.status.toLowerCase() === 'done'
		) {
			dispatch(setIsPaymentMode(false));
			dispatch(clearDineIn());
			setPaymentState('');
			dispatch(clearPaymentDetails());
			dispatch(setShowAmountSplit(false));
			dispatch(setTotalBill(0));
			navigation.navigate(screenNames.paymentSuccess, {
				amount: parseFloat(QRCodeData.amount).toFixed(2),
				id: QRCodeData.id,
				remainingAmount: 0,
			});
			resetOrderDetails();
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [QRCodeData.id, QRCodeData.type, orderDetails]);

	async function getDineStatus() {
		const options = {
			headers: getAuthHeaders(),
		};
		Axios.get(
			endpoints.vipService.getOrderStatus(QRCodeData.id),
			options,
		).then(response => {
			if (response?.status === responseCodes.ok) {
				setPaymentState(response?.data?.results?.status);
			}
		});
	}
	async function getSingleStatus() {
		const options = {
			headers: getAuthHeaders(),
		};
		axios
			.get(endpoints.vipService.getSingleStatus(QRCodeData.id), options)
			.then(response => {
				if (response?.status === responseCodes.ok) {
					setPaymentState(response?.data?.results[0]?.status);
				}
			});
	}
	async function getBulkStatus() {
		const options = {
			headers: getAuthHeaders(),
		};
		axios
			.get(endpoints.vipService.getBulkStatus(QRCodeData.id), options)
			.then(response => {
				if (response?.status === responseCodes.ok) {
					setPaymentState(response?.data?.results?.status);
				}
			});
	}

	function onHomePress() {
		dispatch(setIsPaymentMode(false));
		dispatch(clearDineIn());
		dispatch(setActiveOpenedOrder({}));
		dispatch(setmodify(false));
		dispatch(setRemainingBill(false));
		dispatch(setActiveTab(strings.orderManagement));
	}
	return (
		<>
			<View style={styles.wrapper}>
				<Text
					style={[
						styles.title,
						{
							color: theme?.colors.unSelectedButtonTextColor,
						},
					]}>
					{strings?.qrPaymentWithPhone}
				</Text>
				<View style={styles.qrContainer}>
					<QRCode value={url} size={getScaledFont(440)} />
				</View>
				<View style={styles.instructionsContainer}>
					<Text
						style={[
							styles.header,
							{
								color: theme?.colors.unSelectedButtonTextColor,
							},
						]}>
						{strings.paymentInstructionsHeader}
					</Text>
					<Text
						style={[
							styles.headerText,
							{
								color: theme?.colors.unSelectedButtonTextColor,
							},
						]}>
						{strings.paymentInstructions}
					</Text>
				</View>
			</View>
			<Button
				text="Home"
				onPress={onHomePress}
				style={styles.homeButton}
			/>
		</>
	);
}

const styles = StyleSheet.create({
	instructionsContainer: {marginTop: height(80), marginStart: width(40)},
	qrContainer: {width: '100%', alignItems: 'center'},
	itemsColumnsWrapper: {
		columnGap: width(20),
	},
	itemsContentWrapper: {
		flexGrow: 1,
		paddingHorizontal: width(40),
		paddingBottom: height(20),
		rowGap: height(20),
	},
	itemsWrapper: {
		flex: 1,
		marginTop: height(20),
	},
	title: {
		// color: colors.primary,
		fontFamily: fonts.semiBold,
		fontSize: getScaledFont(24),
		marginBottom: height(40),
		textAlign: 'center',
		width: '100%',
	},
	qrView: {
		flexDirection: 'row',
		alignItems: 'flex-start',
	},
	noButton: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'flex-end',
		paddingVertical: height(20),
		marginEnd: width(20),
	},
	homeButton: {
		width: width(200),
		height: height(60),
		marginEnd: width(10),
	},
	titleBorder: {
		// backgroundColor: colors.accent,
		borderRadius: getScaledFont(2),
		height: height(2),
	},
	wrapper: {
		flex: 1,
	},
	header: {
		// color: colors.primary,
		fontFamily: fonts.semiBold,
		fontSize: getScaledFont(20),
	},
	headerText: {
		// color: colors.primary,
		fontFamily: fonts.medium,
		fontSize: getScaledFont(16),
		lineHeight: height(19),
	},
});

export default memo(QRCodeView);
