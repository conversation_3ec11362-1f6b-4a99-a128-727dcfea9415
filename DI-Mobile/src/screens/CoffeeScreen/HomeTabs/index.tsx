import React, {memo, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {useAnimatedStyle} from 'react-native-reanimated';
import {getScaledFont, height, width} from '../../../global/fonts';
import screenNames from '../../../global/screenNames';
import strings from '../../../global/strings';
import useNavigation from '../../../hooks/useNavigation';
import {useAppDispatch, useAppSelector} from '../../../store';
import {
  clearDineIn,
  setActiveTab,
  setCategoriesFetched,
  setConfirmModal,
  setmodify,
} from '../../../store/slice/home.slice';
import HomeTab, {buttonWidth} from './HomeTab';
import {setActiveOpenedOrder} from '../../../store/slice/orders.slice';
import {setModalError} from '../../../store/slice/app.slice';
import {useTheme} from '../../../global/theme';
import {setShowAmountSplit} from '../../../store/slice/amountSplit.slice';
import {deleteFloorById} from '../../../store/thunk/home.thunk';
import useSpinners from '../../../hooks/useSpinners';

function HomeTabs(props: IHomeTabsProps) {
  const dispatch = useAppDispatch();
  const {theme} = useTheme();
  const navigation = useNavigation();
  const {addOneSpinner, removeOneSpinner} = useSpinners();
  const {floorData} = useAppSelector(state => state.home);
  const {orderItems, dineIn, categoriesFetched, isEnableVip} = useAppSelector(
    state => state.home,
  );
  const {activeTab, isDineIn, orderType, tabPositions} = useAppSelector(
    state => state.home,
  );
  let localActiveTab = strings.orderManagement;
  const [viewLayout, setViewLayout] = useState({width: 0, height: 0});
  const onViewLayout = (event: any) => {
    const {width, height} = event.nativeEvent.layout;
    setViewLayout({width, height});
  };
  const {openedOrderDetails} = useAppSelector(state => state.order);
  function onPressOrderManagement() {
    localActiveTab = strings.orderManagement;
    if (isDineIn) {
      if (
        (isDineIn || orderType) &&
        (orderItems?.length > 0 || dineIn?.length > 0)
      ) {
        dispatch(
          setConfirmModal({
            boolean: true,
            activeScreenName: strings.orderManagement,
          }),
        );
        dispatch(setActiveOpenedOrder({}));
        return;
      }
      dispatch(setShowAmountSplit(false)), dispatch(clearDineIn());
    }
    dispatch(setActiveTab(strings.orderManagement));
    dispatch(setShowAmountSplit(false));
    dispatch(setCategoriesFetched(!categoriesFetched));
  }

  function onPressTableManagement() {
    //updating table status to available
    if (!isDineIn) {
      if (
        (isDineIn || orderType) &&
        (orderItems?.length > 0 || dineIn?.length > 0)
      ) {
        dispatch(
          setConfirmModal({
            boolean: true,
            activeScreenName: strings.tableManagement,
          }),
        );
        return;
      }
      dispatch(setActiveTab(strings.tableManagement));
      dispatch(setActiveOpenedOrder({}));
      dispatch(setCategoriesFetched(!categoriesFetched));
      dispatch(setShowAmountSplit(false));
      localActiveTab = strings.tableManagement;
    }
  }

  function handleVip() {
    navigation.navigate(screenNames.vipService);
    dispatch(setActiveTab(strings.orderManagement));
    dispatch(setActiveOpenedOrder({}));
    dispatch(clearDineIn());
    dispatch(setShowAmountSplit(false));
    dispatch(setmodify(false));
    dispatch(setCategoriesFetched(!categoriesFetched));
  }
  function onPressVipServiceManagement() {
    if (
      (isDineIn || orderType) &&
      (orderItems?.length > 0 || dineIn?.length > 0)
    ) {
      dispatch(setShowAmountSplit(false));
      dispatch(
        setModalError({
          type: 'edit',
          title: strings.removeTable,
          label: strings.ok,
          action: () => undefined,
          ok: async () => {
            if (dineIn?.length > 0 && openedOrderDetails?.id === undefined) {
              dispatch(setModalError(undefined));
              addOneSpinner();
              const {data, error} = await deleteFloorById(floorData);
              if (error) {
                removeOneSpinner();
                dispatch(
                  setModalError({
                    type: 'message',
                    title: error?.response?.data?.details,
                    label: strings.ok,
                    action: () => undefined,
                  }),
                );
              } else {
                removeOneSpinner();
                handleVip();
              }
            } else {
              handleVip();
            }
          },
        }),
      );
    } else {
      dispatch(setShowAmountSplit(false));
      navigation.navigate(screenNames.vipService);
    }
  }
  const activeTabAnimation = useAnimatedStyle(() => {
    const layout = tabPositions[strings.tableManagement];
    return {
      left:
        localActiveTab === strings.orderManagement && !isDineIn
          ? props.left.value
          : layout,
    };
  }, [isDineIn, tabPositions]);
  const requiredWidth = isEnableVip ? 635 : 405;
  return (
    <View
      style={[
        styles.wrapper,
        {
          width: width(requiredWidth),
          backgroundColor: theme?.colors.tabBackgroundColor,
        },
      ]}>
      <View style={styles.buttonsWrapper}>
        <HomeTab
          active={activeTab === strings.orderManagement && !isDineIn}
          title={strings.orderManagement}
          onPress={onPressOrderManagement}
        />
        {activeTab !== strings.orderManagement ? (
          <View
            style={[
              styles.divider,
              {
                borderColor: theme.colors.enableButtonBackgroundColor,
              },
            ]}
          />
        ) : null}
        <HomeTab
          active={
            activeTab === strings.tableManagement ||
            (activeTab === strings.orderManagement && isDineIn)
          }
          title={strings.tableManagement}
          onPress={onPressTableManagement}
        />
        {isEnableVip ? (
          <>
            {activeTab === strings.orderManagement ? (
              <View
                style={[
                  styles.divider,
                  {
                    borderColor: theme.colors.enableButtonBackgroundColor,
                  },
                ]}
              />
            ) : null}
            <HomeTab
              title={strings.vipServiceManagement}
              onPress={onPressVipServiceManagement}
            />
          </>
        ) : null}
      </View>
      <Animated.View
        onLayout={onViewLayout}
        style={[
          styles.button,
          activeTabAnimation,
          {
            backgroundColor: theme?.colors.selectedButtonBackground,
          },
        ]}
      />
    </View>
  );
}
const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    borderRadius: getScaledFont(8),
    height: height(55),
    justifyContent: 'center',
    width: width(buttonWidth),
  },
  buttonsWrapper: {
    alignItems: 'center',
    flexDirection: 'row',
    position: 'absolute',
    width: '100%',
    zIndex: 1,
  },
  divider: {
    borderWidth: getScaledFont(1),
    height: height(32),
    marginEnd: width(10),
  },
  wrapper: {
    alignItems: 'center',
    alignSelf: 'center',
    borderRadius: getScaledFont(8),
    flexDirection: 'row',
    height: height(56),
    width: width(615),
    flexWrap: 'wrap',
  },
});

export default memo(HomeTabs);
