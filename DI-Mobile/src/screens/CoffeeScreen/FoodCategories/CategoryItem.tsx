/* eslint-disable react-native/no-inline-styles */
import React, { memo, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, Image, Pressable, StyleSheet, Text, View } from 'react-native';
import Animated, {
	Easing,
	useAnimatedStyle,
	useSharedValue,
	withTiming,
} from 'react-native-reanimated';
import uuid from 'react-native-uuid';
import { fonts } from '../../../assets';
import colors from '../../../global/colors';
import { getScaledFont, height, width } from '../../../global/fonts';
import { useTheme } from '../../../global/theme';
import { useAppDispatch, useAppSelector } from '../../../store';
import {
	addOrderItem,
	addTocartGuest,
	setCoffeeUpdate,
	setExpandedItem,
	updateOrderItem,
	updateTocartGuest,
} from '../../../store/slice/home.slice';
import OrderCounter from '../../HomeScreen/CurrentOrder/OrderCounter';

const animationDuration = 500;

function CategoryItem(props: any) {
	const { t } = useTranslation();
	const { theme } = useTheme();
	const { data, onPress, onAddToBilling, isExpanded } = props as any;
	const dispatch = useAppDispatch();
	const { orderType, isUpdate, isDineIn, activeGuest, activeCurrency } =
		useAppSelector(state => state.home);
	const [update, setUpdate] = useState(false);
	const animatedHeight = useSharedValue(height(300));
	const animatedBottomHeight = useSharedValue(0);
	const [count, setCount] = useState(1);
	const listRef = useRef<any>();
	const [selectedOption, setSelectedOptions] = useState<any>([]);
	function generateShortId() {
		const uuidString = String(uuid?.v4());
		const shortId = uuidString?.replace(/-/g, '').slice(0, 4);
		return shortId;
	}
	useEffect(() => {
		if (isUpdate?.id) {
			setCount(isUpdate?.orderQuantity);
			setUpdate(true);
			const selectedArray = isUpdate?.itemData?.selectedOptions;
			const updateSelected = {
				selectedOptions: selectedArray,
			};
			setSelectedOptions(updateSelected);
		} else {
			setUpdate(false);
			setCount(1);
		}
		listRef?.current?.scrollToOffset({
			animated: false,
			offset: 0,
		});
	}, [isUpdate]);
	function onItemPress() {
		const selectedOptions = selectedOption?.selectedOptions;
		const updatedData = {
			...data,
			itemId: generateShortId(),
			orderQuantity: count,
			kitchen_status: 'Pending',
			selectedOptions,
			selectedModifiers: [],
			optionsPrice: 0,
		};
		if (isDineIn) {
			dispatch(
				addTocartGuest({
					activeIndex: activeGuest,
					orderItem: updatedData,
				}),
			);
			return;
		}
		dispatch(addOrderItem(updatedData));
	}
	function clearAll() {
		setUpdate(false);
		setSelectedOptions([]);
		dispatch(setCoffeeUpdate([]));
		dispatch(setExpandedItem(null));
	}
	function handleUpdate() {
		const selectedOptions = selectedOption?.selectedOptions;
		const updatedData = {
			...data,
			itemId: isUpdate?.id,
			orderQuantity: count,
			kitchen_status: 'Pending',
			selectedOptions,
			selectedModifiers: [],
			optionsPrice: 0,
		};
		if (isDineIn) {
			dispatch(
				updateTocartGuest({
					activeIndex: activeGuest,
					orderItem: updatedData,
				}),
			);
			clearAll();
			return;
		}
		dispatch(updateOrderItem(updatedData));
		clearAll();
	}

	function handleAddItems() {
		onAddToBilling();
		onItemPress();
		setUpdate(false);
		setSelectedOptions([]);
		dispatch(setCoffeeUpdate([]));
	}

	function handleViewDetails() {
		onPress();
		setUpdate(false);
		setSelectedOptions([]);
		dispatch(setCoffeeUpdate([]));
	}

	useEffect(() => {
		animatedHeight.value = withTiming(
			isExpanded ? height(440) : height(300),
			{
				duration: animationDuration,
				easing: Easing.linear,
			},
		);
	}, [animatedHeight, isExpanded]);

	useEffect(() => {
		animatedBottomHeight.value = withTiming(isExpanded ? 100 : 0, {
			duration: animationDuration,
			easing: Easing.linear,
		});
	}, [animatedBottomHeight, isExpanded]);

	const animatedStyle = useAnimatedStyle(() => {
		return {
			height: animatedHeight.value,
		};
	});
	const animatedBottomWrapperStyle = useAnimatedStyle(() => {
		return {
			height: animatedBottomHeight.value,
		};
	});

	function handleAddItem(item: any, subItem: any) {
		const handleSugarPress = () => {
			setSelectedOptions((prevItems: any) => {
				const selectedOptions = prevItems?.selectedOptions || [];
				const existingOptionSetIndex = selectedOptions?.findIndex(
					(optionSet: any) =>
						optionSet?.option_set_name === item?.option_set_name,
				);
				if (existingOptionSetIndex !== -1) {
					const updatedOptionSet = {
						...selectedOptions[existingOptionSetIndex],
						options: [{ ...subItem }],
					};
					const updatedSelectedOptions = [...selectedOptions];
					updatedSelectedOptions[existingOptionSetIndex] =
						updatedOptionSet;
					return {
						...prevItems,
						selectedOptions: updatedSelectedOptions,
					};
				}
				const newOptionSet = {
					id: item.id,
					option_set_name: item?.option_set_name,
					options: [{ ...subItem }],
				};
				const updatedSelectedOptions = [
					...selectedOptions,
					newOptionSet,
				];
				return {
					...prevItems,
					selectedOptions: updatedSelectedOptions,
				};
			});
		};
		const isSelected = selectedOption?.selectedOptions?.some(
			(innerItem: any) =>
				innerItem.options.some(
					(option: any) =>
						option?.option_name === subItem?.option_name &&
						innerItem?.option_set_name === item?.option_set_name,
				),
		);
		const selected = isSelected ? 1 : 0.3;
		const color = isSelected ? '#fbf1e8' : colors.lightGrey;
		const bordercolor = isSelected ? '#f2c8ae' : colors.lightGrey;
		const textColor = isSelected ? colors.accent500 : colors.black;
		return (
			<Pressable
				style={[
					styles.itemBtn,
					{ backgroundColor: color, borderColor: bordercolor },
				]}
				onPress={handleSugarPress}>
				<Text
					style={[
						styles.innerTxt,
						{ opacity: selected, color: textColor },
					]}>
					{subItem?.option_name.includes('%')
						? subItem?.option_name
						: subItem?.option_name + '%'}
				</Text>
			</Pressable>
		);
	}

	function increment() {
		setCount(count + 1);
	}
	function decrement() {
		setCount(count - 1);
	}

	const renderItem = ({ item }: any) => {
		return (
			<View style={styles.sugarWrapper}>
				<Text style={[styles.priceTxt, styles.itemTxt]}>
					{item?.option_set_name}
				</Text>
				<View style={styles.bottomWrapper}>
					{item?.options?.map((subitem: any) =>
						handleAddItem(item, subitem),
					)}
				</View>
			</View>
		);
	}

	return (
		<Animated.View
			disabled={!orderType}
			key={data?.id}
			style={[styles.wrapper, animatedStyle]}>
			<View style={styles.topWrapper}>
				<Image
					source={{ uri: data?.image_url }}
					style={styles.imageWrapper}
				/>
				<View style={styles.detailWrapper}>
					<Text style={styles.text} numberOfLines={3}>
						{data?.item_name}
					</Text>
					{data?.description?.trim() && (
						<Text style={styles.descriptionTxt} numberOfLines={3}>
							{data?.description?.trim()}
						</Text>
					)}
					<Text style={styles.amttext} numberOfLines={3}>
						{activeCurrency}
						{data?.price}
					</Text>
				</View>
			</View>
			<Animated.View
				style={[styles.hiddenWrapper, animatedBottomWrapperStyle]}>
				<View style={styles.bottomWrapper}>
					<FlatList data={data?.options}
						ref={listRef}
						horizontal
						renderItem={renderItem}
					/>
				</View>
			</Animated.View>
			<View style={[styles.bottomCountWrapper]}>
				{isExpanded ? (
					<OrderCounter
						value={count}
						onDecrease={decrement}
						onIncrease={increment}
					/>
				) : null}
				<Pressable
					style={[
						styles.buttonWrapper,
						{
							backgroundColor: isExpanded
								? colors.accent
								: `${colors.lightGrey}${'30'}`,
							marginStart: isExpanded ? width(25) : 0,
						},
					]}
					onPress={
						isExpanded && update
							? handleUpdate
							: isExpanded
								? handleAddItems
								: handleViewDetails
					}>
					<Text
						style={[
							styles.detailTxt,
							{ color: isExpanded ? colors.white : colors.black },
						]}>
						{isExpanded && update
							? t('updateToBilling')
							: isExpanded
								? t('addToBilling')
								: t('viewDetails')}
					</Text>
				</Pressable>
			</View>
		</Animated.View>
	);
}

const styles = StyleSheet.create({
	text: {
		color: colors.black,
		fontFamily: fonts.medium,
		fontWeight: '500',
		fontSize: getScaledFont(19),
		lineHeight: getScaledFont(19.36),
		textAlign: 'left',
		marginTop: height(2),
	},
	bottomCountWrapper: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'flex-start',
	},
	descriptionTxt: {
		color: colors.grey,
		fontFamily: fonts.semiBold,
		fontWeight: '600',
		fontSize: getScaledFont(14),
		textAlign: 'left',
		marginTop: height(3),
		marginBottom: height(10),
	},
	sugarWrapper: {
		marginEnd: width(70),
		alignItems: 'flex-start',
		justifyContent: 'space-evenly',
	},
	hiddenWrapper: {
		width: '100%',
		overflow: 'hidden',
		marginTop: '47%',
	},
	itemTxt: {
		marginHorizontal: width(5),
		fontFamily: fonts.bold,
		fontWeight: 'bold',
	},
	emptyWrapper: { marginTop: height(110) },
	itemBtn: {
		height: getScaledFont(40),
		width: getScaledFont(40),
		borderRadius: getScaledFont(25),
		borderWidth: getScaledFont(1),
		alignItems: 'center',
		justifyContent: 'center',
		marginHorizontal: width(5),
		marginTop: height(20),
	},
	innerTxt: {
		color: colors.black,
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		fontSize: getScaledFont(12),
	},
	buttonWrapper: {
		height: height(55),
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: `${colors.lightGrey}${'30'}`,
		borderRadius: height(55 / 2),
	},
	detailTxt: {
		marginTop: height(3),
		color: colors.black,
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		fontSize: getScaledFont(19),
		marginBottom: height(5),
	},
	amttext: {
		color: colors.black,
		fontFamily: fonts.bold,
		fontSize: getScaledFont(32),
		textAlign: 'left',
		fontWeight: 'bold',
		marginTop: height(10),
	},
	imageWrapper: {
		height: height(150),
		width: '35%',
		borderRadius: getScaledFont(8),
	},
	detailWrapper: {
		width: '60%',
		marginStart: width(10),
		flexDirection: 'column',
		alignItems: 'flex-start',
		justifyContent: 'flex-start',
	},
	wrapper: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: getScaledFont(8),
		height: 'auto',
		justifyContent: 'center',
		paddingHorizontal: width(30),
		paddingVertical: height(20),
		marginTop: height(20),
		width: '48%',
		overflow: 'hidden',
		marginEnd: width(20),
	},
	topWrapper: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		position: 'absolute',
		top: height(20),
	},
	bottomWrapper: {
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	priceTxt: {
		marginTop: getScaledFont(8),
		color: colors.black,
	},
	viewwrapper: {
		alignItems: 'center',
		borderRadius: getScaledFont(8),
		height: height(152),
		justifyContent: 'center',
		paddingHorizontal: width(2),
		paddingVertical: height(2),
		width: width(152),
	},
});

export default memo(CategoryItem);
