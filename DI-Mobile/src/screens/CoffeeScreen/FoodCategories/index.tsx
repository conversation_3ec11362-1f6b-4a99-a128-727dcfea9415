/* eslint-disable react-hooks/exhaustive-deps */
import React, {memo, useEffect, useMemo, useState} from 'react';
import {
	FlatList,
	LayoutChangeEvent,
	ListRenderItemInfo,
	StyleSheet,
	View,
} from 'react-native';
import Animated, {useSharedValue} from 'react-native-reanimated';
import {fonts} from '../../../assets';
import {icons} from '../../../assets/images';
import colors from '../../../global/colors';
import {getScaledFont, height, width} from '../../../global/fonts';
import screenNames from '../../../global/screenNames';
import strings from '../../../global/strings';
import {useTheme} from '../../../global/theme';
import useNavigation from '../../../hooks/useNavigation';
import {useAppDispatch, useAppSelector} from '../../../store';
import {setModalError} from '../../../store/slice/app.slice';
import {clearDineIn, setTokenExpired} from '../../../store/slice/home.slice';
import {setToken} from '../../../store/slice/user.slice';
import CategoriesScroller from './CategoriesScroller';
import CoffeeCategory from './CoffeeCategory';

export const categories = [
	{
		id: 1,
		name: 'All menu',
		image: icons.bubble_Tea,
	},
	{
		id: 2,
		name: 'Coffee',
		image: icons.coffee_Cup,
	},
	{
		id: 3,
		name: 'Milky milk',
		image: icons.glass,
	},
	{
		id: 4,
		name: 'Bobaan',
		image: icons.soda,
	},
	{
		id: 5,
		name: 'Ice cream',
		image: icons.iceCream,
	},
	{
		id: 6,
		name: 'Dessert',
		image: icons.cake,
	},
];
function CoffeeCategories() {
	const {theme} = useTheme();
	const leftX = useSharedValue(0);
	const {orderType, isExpired} = useAppSelector((state: any) => state.home);
	const dispatch = useAppDispatch();
	const navigation = useNavigation();
	const [rows, setRows] = useState<IFoodCategory[][]>([]);
	const [sWidth, setSWidth] = useState(1);
	const [vWidth, setVWidth] = useState(1);
	const [selectedCategoryId, setSelectedCategoryId] = useState(null);

	const disbleEvents = useMemo(() => (orderType ? false : true), [orderType]);

	useEffect(() => {
		const numRows = Math.ceil(categories?.length);
		const row1 = categories?.slice(0, numRows);
		setRows([row1]);
	}, [categories]);

	useEffect(() => {
		if (isExpired) {
			dispatch(
				setModalError({
					type: 'message',
					title: strings.sessionExpired,
					label: strings.ok,
					action: () => expired(),
				}),
			);
		}
	}, [dispatch, isExpired]);

	function expired() {
		dispatch(clearDineIn());
		dispatch(setToken(undefined));
		dispatch(setTokenExpired(false));
		navigation.navigate(screenNames.pin);
	}
	function onVWidth(e: LayoutChangeEvent) {
		setVWidth(e.nativeEvent.layout.width);
	}

	function renderCategory({item}: ListRenderItemInfo<any>) {
		return (
			<CoffeeCategory
				disable={disbleEvents}
				data={item}
				isSelected={item?.id === selectedCategoryId}
				onPress={() => setSelectedCategoryId(item?.id)}
			/>
		);
	}
	const keyExtractor = (item: any) => `${item?.id}`;

	function handleScroll(scrollX: any) {
		const {nativeEvent} = scrollX;
		leftX.value = (nativeEvent.contentOffset.x / vWidth) * 100;
	}
	function onScrollViewLayout(e: LayoutChangeEvent) {
		setSWidth(e.nativeEvent.layout.width);
	}
	return (
		<View style={[styles.wrapper]}>
			<Animated.ScrollView
				horizontal
				bounces={false}
				onScroll={handleScroll}
				onLayout={onScrollViewLayout}
				style={styles.scrollView}
				contentContainerStyle={styles.scrollViewContent}
				showsHorizontalScrollIndicator={false}>
				<View onLayout={onVWidth}>
					{rows?.map((row, index) => (
						<FlatList
							horizontal
							data={row}
							scrollEnabled={false}
							renderItem={renderCategory}
							keyExtractor={keyExtractor}
							key={`Food-Category-Row-${index}`}
						/>
					))}
				</View>
			</Animated.ScrollView>
			<CategoriesScroller
				position={leftX}
				wrapperWidth={sWidth}
				viewWidth={vWidth}
			/>
		</View>
	);
}

const styles = StyleSheet.create({
	scrollView: {
		flex: 1,
		marginBottom: height(20),
	},
	scrollViewContent: {
		flexGrow: 1,
		paddingHorizontal: width(40),
	},
	title: {
		color: colors.primary,
		fontFamily: fonts.bold,
		fontWeight:'bold',
		fontSize: getScaledFont(18),
		lineHeight: getScaledFont(27),
	},
	titleBorder: {
		backgroundColor: colors.accent,
		borderRadius: getScaledFont(2),
		height: height(2),
	},
	titleWrapper: {
		alignSelf: 'flex-start',
		marginLeft: width(40),
	},
	wrapper: {
		height: height(230),
		marginBottom: height(20),
		paddingBottom: height(24),
		paddingTop: height(20),
		width: '100%',
	},
});

export default memo(CoffeeCategories);
