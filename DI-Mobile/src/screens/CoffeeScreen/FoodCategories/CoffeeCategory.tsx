/* eslint-disable react-native/no-inline-styles */
import React, {memo} from 'react';
import {Image, Pressable, StyleSheet, Text, View} from 'react-native';
import {fonts} from '../../../assets';
import colors from '../../../global/colors';
import {getScaledFont, height, width} from '../../../global/fonts';
import {useTheme} from '../../../global/theme';
import {useAppDispatch, useAppSelector} from '../../../store';

function CoffeeCategory(props: any) {
	const dispatch = useAppDispatch();
	const {theme} = useTheme();
	const {selectedCategory} = useAppSelector(state => state.home);
	const {data, disable, isSelected, onPress} = props;
	return (
		<Pressable
			style={[
				styles.wrapper,
				{
					backgroundColor: isSelected ? '#fbf1e8' : colors.white,
					borderColor: isSelected ? '#f2c8ae' : colors.white,
				},
			]}
			key={data?.id}
			onPress={onPress}>
			<View>
				<View style={[styles.viewWrapper]}>
					<Image
						source={{uri: data?.image}}
						style={styles.imageView}
					/>
					<Text
						style={[
							styles.name,
							{color: isSelected ? '#8d6657' : colors.grey},
						]}>
						{data?.name}
					</Text>
				</View>
			</View>
		</Pressable>
	);
}

const styles = StyleSheet.create({
	activeWrapper: {
		borderColor: colors.accent,
		borderWidth: getScaledFont(2.5),
	},
	name: {
		color: colors.black,
		fontFamily: fonts.bold,
		fontWeight:'bold',
		fontSize: getScaledFont(14),
		lineHeight: getScaledFont(21.78),
		textAlign: 'center',
		textTransform: 'capitalize',
	},
	titleWrapper: {
		alignItems: 'center',
		borderRadius: getScaledFont(8),
		height: height(100),
		justifyContent: 'center',
		overflow: 'hidden',
		resizeMode: 'contain',
		width: width(100),
	},
	viewWrapper: {
		alignItems: 'center',
	},
	imageView: {
		height: '75%',
		width: '100%',
		resizeMode: 'contain',
	},
	wrapper: {
		alignItems: 'center',
		borderRadius: getScaledFont(10),
		height: height(150),
		justifyContent: 'center',
		width: width(110),
		marginHorizontal: width(10),
		backgroundColor: colors.white,
		borderColor: colors.grey,
		borderWidth: getScaledFont(3),
	},
});

export default memo(CoffeeCategory);
