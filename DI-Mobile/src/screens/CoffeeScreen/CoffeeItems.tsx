/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-native/no-inline-styles */
import React, { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
	FlatList,
	ListRenderItemInfo,
	StyleSheet,
	Text,
	View,
} from 'react-native';
import { fonts } from '../../assets';
import colors from '../../global/colors';
import { getScaledFont, height, width } from '../../global/fonts';
import { useTheme } from '../../global/theme';
import { useAppDispatch, useAppSelector } from '../../store';
import { setExpandedItem } from '../../store/slice/home.slice';
import CategoryItem from './FoodCategories/CategoryItem';

function CoffeeItems() {
	const { t } = useTranslation();
	const { categoryItems, orderType, expandedItem } = useAppSelector(
		state => state.home,
	);
	const { theme } = useTheme();
	const keyExtractor = (item: ICategoryItem) => `${item?.id}`;
	const [search, setSearch] = useState('');
	const dispatch = useAppDispatch();
	function renderItem({ item }: ListRenderItemInfo<any>) {
		return (
			<CategoryItem
				data={item}
				isExpanded={item?.id === expandedItem}
				onPress={() =>
					dispatch(
						setExpandedItem(
							item?.id === expandedItem ? null : item?.id,
						),
					)
				}
				onAddToBilling={() => dispatch(setExpandedItem(null))}
			/>
		);
	}

	return (
		<View style={styles.wrapper}>
			{/* <View style={styles.categorySearch}>
				<View style={styles.categoresWrapper}>
					<Text
						style={[
							styles.title,
							{
								color: theme?.colors.unSelectedButtonTextColor,
							},
						]}>
						{'Welcome to Coffeehouse'}
					</Text>
					<Text
						style={[
							styles.title,
							{
								color: colors.lightGrey,
							},
							styles.subTitle,
						]}>
						{'Choose the category'}
					</Text>
				</View>
				<View style={styles.searchView}>
					<Search
						style={styles.searchIcon}
						height={getScaledFont(16)}
						width={getScaledFont(16)}
					/>
					<View style={styles.searchOuter}>
					<TextInput
						placeholder="search something"
						value={search}
						placeholderTextColor={colors?.lightGrey}
						style={[
							styles.searchWrapper,
							{
								color: theme?.colors.darkTextColor,
							},
						]}
						onChangeText={(text: any) => setSearch(text)}
					/>
					</View>
				</View>
			</View> */}
			{/* <FoodCategories /> */}
			{/* <View style={styles.titleWrapper}>
				<Text
					style={[
						styles.title,
						{
							color: theme?.colors.textInputFontColor,
						},
					]}>
					{'Coffee Menu'}
				</Text>
				<Text
					style={[
						styles.title,
						{
							color: theme?.colors.bordercolor,
							fontFamily: fonts.regular,
							fontSize: getScaledFont(16),
							fontWeight:'200',
						},
					]}>
					{' 13 Coffee results'}
				</Text>
			</View> */}
			{orderType ? (
				<FlatList
					bounces={false}
					numColumns={2}
					data={categoryItems}
					renderItem={renderItem}
					keyExtractor={keyExtractor}
					style={styles.itemsWrapper}
					contentContainerStyle={styles.itemsContentWrapper}
					columnWrapperStyle={styles.columnWrapper}
					showsVerticalScrollIndicator={false}
					ListEmptyComponent={() => {
						return (<View style={styles.itemEmptyView}>
							<Text
								style={[
									styles.noItemText,
									{
										color: theme?.colors.selectedButtonBackground,
									},
								]}>
								{t('noItemsFound')}
							</Text>
						</View>)
					}}
				/>
			) : (
				<View style={styles.itemEmptyView}>
					<Text
						style={[
							styles.noItemText,
							{
								color: theme?.colors.selectedButtonBackground,
							},
						]}>
						{t('noItemsData')}
					</Text>
				</View>
			)}
		</View>
	);
}

const styles = StyleSheet.create({
	itemsColumnsWrapper: {
		columnGap: width(1),
	},
	itemsContentWrapper: {
		flexGrow: 1,
		paddingBottom: '15%',
	},
	itemsWrapper: {
		flex: 1,
		marginTop: height(20),
	},
	columnWrapper: {
		flex: 1,
		flexGrow: 1,
		justifyContent: 'flex-start',
	},
	categorySearch: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
	},
	itemEmptyView: {
		flex: 0.9,
		alignItems: 'center',
		justifyContent: 'center',
	},
	subTitle: {
		fontSize: getScaledFont(14),
		marginTop: height(10),
		fontWeight: '400',
	},
	noItemText: {
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		textAlignVertical: 'center',
		fontSize: getScaledFont(18),
		lineHeight: getScaledFont(27),
		marginBottom: height(4),
	},
	title: {
		fontFamily: fonts.bold,
		fontWeight: 'bold',
		textAlignVertical: 'center',
		fontSize: getScaledFont(18),
	},

	titleBorder: {
		borderRadius: getScaledFont(2),
		height: height(2),
	},
	titleWrapper: {
		marginHorizontal: width(40),
		flexDirection: 'row',
		alignItems: 'flex-start',
		justifyContent: 'space-between',
	},
	categoresWrapper: {
		marginLeft: width(40),
		marginRight: width(21),
	},
	searchIcon: {
		paddingStart: getScaledFont(40),
	},
	searchView: {
		flexDirection: 'row',
		marginHorizontal: width(50),
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: getScaledFont(8),
		height: height(60),
		justifyContent: 'center',
	},
	searchOuter: {
		width: width(300),
		marginStart: width(-10),
		paddingHorizontal: width(20),
		height: height(60),
		justifyContent: 'center',
	},
	searchWrapper: {
		fontSize: getScaledFont(17),
		fontFamily: fonts.medium,
		fontWeight: '500',
		textAlignVertical: 'center',
		lineHeight: getScaledFont(16),
	},
	wrapper: {
		flex: 1,
		marginStart: width(15),
	},
});

export default memo(CoffeeItems);
