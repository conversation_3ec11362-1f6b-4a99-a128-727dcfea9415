import React, {memo, useMemo, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {RenderTitle} from '.';
import {Button} from '../../components/button';
import CategoryCard from '../../components/category/CategoryCard';
import SubCategoryCard from '../../components/category/SubCategoryCard';
import CheckoutContainer from '../../components/payment/CheckoutContainer';
import colors from '../../global/colors';
import {getScaledFont, height, width} from '../../global/fonts';

const OrdersContainer = memo(
	({
		categories,
		selectedCategoryItems,
		subCategoryItmes,
		price,
		paymentDetails,
		checkoutItems,
		activeMenu,
	}: any) => {
		const hasCategories = useMemo(
			() => categories && Object.keys(categories)?.length > 0,
			[categories],
		);
		const hasSubCategories = useMemo(
			() => subCategoryItmes && Object.keys(subCategoryItmes)?.length > 0,
			[subCategoryItmes],
		);
		const renderItems = ({item}: any) => {
			return (
				<Button
					fontSize={getScaledFont(16)}
					fontColor={
						item?.type === 'All' ? colors.white : colors.black
					}
					onPress={() => {}}
					text={item?.type}
					style={[
						styles.categoryBtn,
						{
							backgroundColor:
								item?.type === 'All'
									? colors.accent
									: colors.transparent,
						},
					]}
				/>
			);
		};
		return (
			<View style={styles.container}>
				{/* Checkoutcontainer */}
				<CheckoutContainer
					{...{price, paymentDetails, checkoutItems, activeMenu}}
				/>
				{/* all categories */}
				<View style={[styles.categoryItemsContainer]}>
					<View style={[styles.leftSpacing]}>
						{hasCategories ? (
							<View style={styles.column}>
								<View style={styles.categories}>
									{/* <Animated.FlatList
										data={categories?.allCategories || []}
										horizontal
										renderItem={renderItems}
										contentContainerStyle={
											styles.categoryChipsContainer
										}
									/> */}
								</View>
								<CategoryCard
									items={selectedCategoryItems?.categories}
									style={[styles.categoryListedItems]}
									numRows={2}
								/>
							</View>
						) : null}
						{hasSubCategories ? (
							<View style={styles.column}>
								<View
									style={[
										styles.categories,
										styles.paddingTopSpace,
									]}>
									<RenderTitle title={'Items'} />
								</View>
								<SubCategoryCard items={subCategoryItmes} />
							</View>
						) : null}
					</View>
				</View>
			</View>
		);
	},
);

export default OrdersContainer;

const styles = StyleSheet.create({
	container: {
		width: '100%',
		height: '100%',
		marginTop: height(24),
		flexDirection: 'row',
		paddingHorizontal: 40,
	},
	subItemsImageStyle: {
		width: width(152),
		height: height(152),
	},

	categoryItemsContainer: {
		width: '100%',
		height: '100%',
	},
	paddingTopSpace: {
		paddingVertical: width(20),
	},
	categoryConatiner: {},
	leftSpacing: {
		paddingHorizontal: width(40),
	},
	categories: {
		flexDirection: 'row',
		alignItems: 'center',
	},
	categoryChipsContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingLeft: width(10),
	},
	categoryBtn: {
		paddingHorizontal: width(16),
		paddingVertical: height(8),
		borderColor: colors.accent,
		borderWidth: 1,
		marginLeft: width(10),
		borderRadius: width(12),
	},
	column: {
		flexDirection: 'column',
	},
	categoryListedItems: {
		paddingVertical: height(31),
		borderBottomWidth: 2,
		borderBottomColor: '#d7d7d7',
		height: height(292),
	},
});
