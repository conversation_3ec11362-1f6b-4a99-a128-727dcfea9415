import SummaryTitle from "@/components/atoms/SummaryTitle/SummaryTitle";

export default function TotalOrderDetails(props) {
  const { obj, title, orderDetails } = props;
  return (
    <div className="flex flex-col border-b border-gray-400 mb-4 pb-4">
      <SummaryTitle title={title} />
      {orderDetails?.orderData?.map((each, i) => {
        return (
          <div key={i} className="flex flex-col py-2 gap-4">
            <div className="flex justify-between w-full">
              <div className="flex flex-col justify-start w-[75%]">
                <p className="text-[#404040] font-semibold text-base leading-5 ">
                  {each?.itemName}
                </p>
                <p className="text-[#404040] font-normal text-sm leading-5 w-3/4">
                  ({each?.modifiersData.join(", ")})
                </p>
                <p className="text-[#404040] font-normal text-sm leading-5">
                  Quantity: {each?.quantity}
                </p>
              </div>
              <p className="text-[#404040] font-normal text-sm leading-5">
                ${each?.price}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
}
