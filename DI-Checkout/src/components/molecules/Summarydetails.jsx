"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import SummaryTitle from "../atoms/SummaryTitle/SummaryTitle";
import SummaryText from "../atoms/SummaryText/SummaryText";
import CardImage from "./CardImage/CardImage";

export function Summarydetails(props) {
  const router = useRouter();
  const { obj, title, isPaymentInfo } = props;

  const [headerData, setHeaderData] = useState({});

  useEffect(() => {
    if (typeof window !== "undefined") {
      setHeaderData(
        window.sessionStorage &&
          JSON.parse(sessionStorage?.getItem("orderData"))
      );
    }
  }, []);

  const handleEditClick = () => {
    const { merchant_id, orderId, amount, currency, orderType } =
      headerData.apiParams;
    router.push(
      `/payment?orderId=${orderId}&amount=${amount}&orderType=${orderType}&currency=${currency}&merchant_id=${merchant_id}`
    );
  };

  const renderPaymentInfo = () => {
    return (
      <div className="flex flex-col border-b border-gray-400 mb-4 pb-4">
        <div className="flex justify-between align-middle">
          <SummaryTitle title={title} />
          <button
            type="button"
            className="text-sm font-semibold text-[#00A6A2] cursor-pointer leading-[19.07px] hover:underline"
            onClick={handleEditClick}
          >
            Edit
          </button>
        </div>
        {obj &&
          Object.keys(obj).map((key) => (
            <>
              {key === "cardNumber" ? (
                <div className="flex items-center gap-2">
                  <CardImage width={20} height={20} value={obj[key]} />
                  <SummaryText
                    key={key}
                    value={`**** ${obj[key]?.slice(-4)}`}
                  />
                </div>
              ) : (
                <SummaryText key={key} value={obj[key]} />
              )}
            </>
          ))}
      </div>
    );
  };

  // Render order summary details
  const renderOrderSummary = () => {
    return (
      <div className="flex flex-col border-b border-gray-400 mb-4 pb-4">
        <SummaryTitle title={title} />
        {obj &&
          Object.keys(obj).map((key) => (
            <SummaryText key={key} value={obj[key]} />
          ))}
      </div>
    );
  };

  return isPaymentInfo ? renderPaymentInfo() : renderOrderSummary();
}
