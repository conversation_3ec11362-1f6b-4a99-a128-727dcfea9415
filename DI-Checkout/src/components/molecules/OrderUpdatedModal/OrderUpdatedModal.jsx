"use client";
import { Button } from "@/components/atoms/Button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@/components/atoms/Dialog/dialog";

export const OrderUpdatedModal = ({ isOpen, onClose, handleProceed }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogTitle className="text-center">Order Changed</DialogTitle>
        <div className="flex flex-col justify-center items-center gap-3">
          <p className="text-base">Your order has been updated.</p>
          <Button
            className="w-fit"
            handleClick={handleProceed}
            isChecked
            value="Proceed"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
