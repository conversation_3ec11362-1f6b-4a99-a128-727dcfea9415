.text_xxs {
  font-size: 10px;
  padding-bottom: 2px;
}

.form_field {
  position: relative;
  width: 100%;
  height: 40px;
  padding-right: 14px;
  padding-left: 14px;
  border: 1px solid #cccccc;
  transition: all 0.1s ease;
  border-radius: 6px;
  margin-bottom: 16px;

  &:hover {
    border-color: #adadad;
    box-shadow: 0px 0px 3px 0 #00000029;
  }

  .unit-label {
    position: absolute;
    pointer-events: none;
    font-weight: bold;
    font-size: 14px;
    color: #555;
    background-color: #e8e8e8;
    border-radius: 4px;
    padding: 9px 10px;
    bottom: 4px;
    left: 4px;
  }

  &.locked::after {
    content: "\e92c";
    font-size: 12px;
    color: #555;
    font-family: "icomoon";
    position: absolute;
    right: 8px;
    bottom: 14px;
  }
}

.focus {
  border-color: #00a6a2 !important;
  &:hover {
    border-color: #00a6a2 !important;
  }
}

.error {
  border-color: #e32b25 !important;
  &:hover {
    border-color: #e32b25 !important;
  }
}
.disabled {
  border-color: #e5e5e5 !important;
  pointer-events: none;
}
.phone-class {
  margin-top: 12px;
}
