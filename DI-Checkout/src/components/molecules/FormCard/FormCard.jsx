import NumberFormat from "react-number-format";
import styles from "./FormCard.module.scss";
import Image from "next/image";
import CheckMark from "../../../../public/assets/checkmark-success.svg";
import ScanIcon from "../../../../public/assets/Scan_Icon.svg";
import { useEffect, useState } from "react";
import { Input } from "@/components/atoms/Input/Input";
import CardImage from "../CardImage/CardImage";

export const FormCard = ({
  id = "",
  className = "",
  label = "",
  message = "",
  error = "",
  errorClassname = "",
  parentComponent,
  disabled = false,
  ShowImage = true,
  isSemibold = false,
  isVendors = false,
  isUSCodeRequired = false,
  value = "",
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [labelTop, setLabelTop] = useState(false);

  const handleBlur = (e) => {
    setIsFocused(false);
    setLabelTop(!!e.target.value);
    const { onBlur } = rest;
    if (typeof onBlur === "function") {
      onBlur(e);
    }
  };

  const handleFocus = (e) => {
    setIsFocused(true);
    setLabelTop(true);
    const { onFocus } = rest;
    if (typeof onFocus === "function") {
      onFocus(e);
    }
  };

  useEffect(() => {
    setLabelTop(!!value);
  }, [value]);

  return (
    <div
      className={`
        ${styles.form_field}
        ${isFocused && styles.focus}
        ${error && styles.error}
        ${disabled && styles.disabled}
        ${className} ${isUSCodeRequired && "!flex group"}`}
    >
      {ShowImage && (
        <CardImage
          className="absolute top-2.5 left-3"
          data-testid="editInvitation_usaFlag"
          value={value}
        />
      )}
      {/* {ShowImage && (
        <Image
          src={ScanIcon}
          className="absolute top-2.5 right-3"
          data-testid="editInvitation_usaFlag"
          alt="scan"
        />
      )} */}
      {label && (
        <label
          className={`px-1 bg-cwhite absolute bg-[#FFF] ${
            labelTop && parentComponent == "editInvitation" && "text-cm3"
          } ${
            labelTop
              ? "left-3"
              : ShowImage
              ? "left-12"
              : isUSCodeRequired
              ? "left-[86px]"
              : "left-3"
          }  transform transition-all duration-100 ease-in
             ${
               isVendors && labelTop
                 ? "-top-[11px] text-[10px]"
                 : labelTop
                 ? `-top-2  ${styles.text_xxs}`
                 : "top-1/4 text-sm"
             }
            ${
              disabled
                ? "text-cgy2"
                : labelTop && parentComponent == "editInvitation"
                ? "text-cm3"
                : labelTop && parentComponent !== "editInvitation"
                ? "text-cgy4"
                : "text-cgy3"
            }`}
          htmlFor={id}
          data-testid="edit_invitation_label"
        >
          {label}
        </label>
      )}
      {isUSCodeRequired && (
        <span
          className={`${
            isFocused
              ? styles.focus
              : error
              ? styles.error
              : "border-cgy1 group-hover:border-cgy2"
          } shrink-0 w-16 flex items-center border-r mr-3 border-solid border-cgy1 h-full`}
        >
          +1
        </span>
      )}
      <NumberFormat
        {...rest}
        id={id}
        data-testid={`${label}_NumberFormat`}
        customInput={Input}
        onFocus={handleFocus}
        value={value}
        onBlur={handleBlur}
        className={`${disabled ? "text-cgy2" : "text-cgy4"} ${
          ShowImage && "pl-9"
        } ${styles.formatWidth} ${isSemibold && "font-semibold"}`}
      />
      <p />
      <span
        className={`
        ${styles.text_xxs} absolute left-3 -bottom-[18px] font-semibold truncate
        ${error && "text-[#E32b25] font-semibold"} ${errorClassname}`}
        data-testid={`errorMessage_${label}`}
      >
        {error || message}
      </span>
    </div>
  );
};

//For Form Field

export function FormField({
  id = "",
  label = "",
  bankNumber = false,
  optional = "",
  message = "",
  error = "",
  showCharCount = true,
  isCapitalize = false,
  disableNumbers = false,
  disableAlphabets = false,
  disableSymbols = false,
  disableSpace = false,
  disablePasswordSymbols = false,
  success = "",
  unit = "",
  children,
  locked = false,
  className = "",
  inputStyles = "",
  errorClassname = "",
  innerIcon = false,
  withIcon,
  disabled = false,
  innerRef,
  maxLength = false,
  placeholder,
  parentComponent,
  lableOpen = false,
  enableComa = false,
  enableDot = false,
  chargeBack = false,
  textRight = false,
  isSemibold = false,
  textColor = "",
  permissionForm = false,
  isPaste = true,
  isCopy = true,
  isCut = true,
  disableUnderScore = false,
  showEditIcon = false,
  isNewEmployee = false,
  isVendors = false,
  "ui-auto": ui_auto = "",
  isValid = false,
  ...props
}) {
  const [isFocused, setIsFocused] = useState(false);
  const [isHover, setIsHover] = useState(false);
  const [labelTop, setLabelTop] = useState(false);
  const [isPassWord, setIsPassWord] = useState("text");

  const handleBlur = (e) => {
    setIsFocused(false);
    setLabelTop(!!e.target.value);
    const { onBlur } = props;
    if (typeof onBlur === "function") {
      onBlur(e);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    setLabelTop(true);
  };

  useEffect(() => {
    if (bankNumber) {
      if (isFocused) {
        setIsPassWord("text");
      } else {
        setIsPassWord("password");
      }
    }
  }, [isFocused]);

  const handleInputKeyDown = (e) => {
    if (disableSymbols) {
      if (enableComa) {
        const s = [...symbols];
        const i = s.indexOf(",");
        if (i > -1) {
          s.splice(i, 1);
        }
        [...s].includes(e.key) && e.preventDefault();
      }
      // return;
      //  }else{
      //   [...symbols].includes(e.key) && e.preventDefault();
      // }
      else if (enableDot) {
        const D = [...symbols];
        const O = D.indexOf(".");
        if (O > -1) {
          D.splice(O, 1);
        }
        [...D].includes(e.key) && e.preventDefault();
      } else {
        [...symbols].includes(e.key) && e.preventDefault();
      }
    }
    //  if (disableSymbols)
    // if (enableDot) {
    //   const D = [...symbols];
    //   const O = D.indexOf('.');
    //   if (O > -1) {
    //     D.splice(O, 1);
    //   }
    //   [...D].includes(e.key) && e.preventDefault();
    // } else {
    //   [...symbols].includes(e.key) && e.preventDefault();
    // }

    if (disableSpace) {
      e.which === 32 && e.preventDefault();
    }
    if (disableNumbers) {
      [...numbers].includes(e.key) && e.preventDefault();
    }
    if (disableAlphabets) {
      [...alphabets].includes(e.key) && e.preventDefault();
    }
    if (disablePasswordSymbols) {
      [...symbolPassCode].includes(e.key) && e.preventDefault();
    }
    if (disableUnderScore) {
      [...underScore].includes(e.key) && e.preventDefault();
    }
  };

  useEffect(() => {
    const { value } = props;
    setLabelTop(isFocused ? isFocused : !!value);
  }, [props]);

  const handleOnPaste = (e) => {
    if (!isPaste) {
      e.preventDefault();
      return false;
    }
  };

  const handleOnCopy = (e) => {
    if (!isCopy) {
      e.preventDefault();
      return false;
    }
  };

  const handleOnCut = (e) => {
    if (!isCut) {
      e.preventDefault();
      return false;
    }
  };

  return (
    <div
      className={`${
        parentComponent == "ApiBusiness" ? styles.form_field_business : ""
      }
      ${parentComponent == "editInvitation" && styles.editInvitation}
        ${styles.form_field}
        ${isFocused && styles.focus}
        ${error && styles.error}
        ${disabled && styles.disabled}
        ${className} group`}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      {!isFocused && isHover && showEditIcon && (
        <button
          className={`icon-edit absolute top-2.5 right-3 text-cm3 invisible group-hover:visible cursor-pointer`}
          data-testid={`iconEdit_${label}`}
          onClick={() => innerRef?.current?.focus()}
        ></button>
      )}
      {isValid && (
        <span className="h-5 w-5">
          <Image
            src={CheckMark}
            className="absolute top-2.5 right-3"
            data-testid="editInvitation_usaFlag"
            alt="Card"
          />
        </span>
      )}
      {label && (
        <label
          className={`px-1 bg-cwhite absolute left-3 transform transition-all duration-100 ease-in bg-[#FFF] ${
            labelTop && parentComponent == "editInvitation" && "text-cm3"
          }
            ${
              isNewEmployee && labelTop
                ? "-top-2 text-[10px] px-[5px]"
                : isVendors && labelTop
                ? `-top-[11px] text-[10px]`
                : permissionForm && labelTop
                ? `-top-2 ${styles.text_xxs}`
                : labelTop
                ? `-top-[9px] ${styles.text_xxs}`
                : chargeBack
                ? "top-1.5 text-sm"
                : "top-1/4 text-sm"
            }
            ${
              disabled
                ? "text-cgy2 pointer-events-none"
                : labelTop && !lableOpen && parentComponent == "ApiBusiness"
                ? "text-cm3"
                : labelTop && !lableOpen && parentComponent !== "ApiBusiness"
                ? "text-cgy4"
                : "text-cgy3"
            }
            `}
          htmlFor={id}
          data-testid={`roleName_${label}`}
        >
          {label == "Enter Role Name" && labelTop ? "Role Name" : label}
          {optional && (
            <span
              className={`ml-2 text-xs   ${
                disabled ? "text-cgy2 pointer-events-none" : "text-cgy4"
              }`}
              data-testid={`optionaLabel_${label}`}
            >
              {optional}
            </span>
          )}
        </label>
      )}
      {lableOpen ? (
        <div className={`flex items-center pt-3`}>
          <Input
            data-testid={`${label}Input_textValue`}
            {...props}
            placeholder={placeholder}
            id={id}
            className={`${isCapitalize && styles.input} ${
              textRight && `text-right`
            } ${textColor} ${inputStyles}`}
            disabled={disabled}
            ref={innerRef}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleInputKeyDown}
            maxLength={maxLength}
            onCopy={handleOnCopy}
            onPaste={handleOnPaste}
            onCut={handleOnCut}
          />
          <span
            className={` ${
              parentComponent == "editInvitation" ? "ml-2" : "-ml-5"
            }`}
            data-testid={`editInvitation_${label}`}
          >
            {children}
          </span>
        </div>
      ) : (
        <Input
          data-testid={`${label?.replace(" ", "_")}_texValue`}
          {...props}
          placeholder={placeholder}
          id={id}
          className={`${isCapitalize && styles.input} ${
            textRight && `text-right `
          }  ${isSemibold && `font-semibold`}${textColor} ${inputStyles}`}
          disabled={disabled}
          ref={innerRef}
          onCopy={handleOnCopy}
          onPaste={handleOnPaste}
          onCut={handleOnCut}
          {...(bankNumber && { type: isPassWord })}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleInputKeyDown}
          maxLength={maxLength}
        />
      )}
      <span
        className={`
        ${styles.text_xxs} absolute left-3 -bottom-[18px] truncate font-semibold
        ${error && "text-[#E32b25]"} ${errorClassname}`}
        data-testid={`${label}_err_msg`}
      >
        {error || message}
      </span>

      <span
        className={`
        ${styles.text_xxs} absolute left-3 -bottom-[18px] truncate font-semibold
         ${errorClassname} ${success && "text-cgn5"} `}
        data-testid={`success_${label}`}
      >
        {success}
      </span>
    </div>
  );
}

export const FormMask = ({
  id = "",
  className = "",
  label = "",
  message = "",
  error = "",
  disabled = false,
  PhoneError = "",
  placeholder = "",
  parentComponent,
  showEditIcon = false,
  errorClassName = "",
  innerRef,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isHover, setIsHover] = useState(false);

  const [labelTop, setLabelTop] = useState(false);

  const handleBlur = (e) => {
    setIsFocused(false);
    setLabelTop(!!e.target.value);
    const { onBlur } = rest;
    if (typeof onBlur === "function") {
      onBlur(e);
    }
  };

  const handleFocus = (e) => {
    setIsFocused(true);
    setLabelTop(true);
    const { onFocus } = rest;
    if (typeof onFocus === "function") {
      onFocus(e);
    }
  };

  useEffect(() => {
    const { value } = rest;
    setLabelTop(isFocused ? isFocused : !!value);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rest?.value]);

  return (
    <div
      className={`
        ${styles.form_field}
        ${isFocused && styles.focus}
        ${error && styles.error}
        ${disabled && styles.disabled}
        ${className} group`}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      {!isFocused && isHover && showEditIcon && (
        <button
          className={`icon-edit absolute top-2.5 right-3 text-cm3 invisible group-hover:visible cursor-pointer`}
          onClick={() => innerRef?.current?.focus()}
        ></button>
      )}
      {label && (
        <label
          className={`px-1 bg-cwhite absolute left-3 transform transition-all duration-100 ease-in bg-[#FFF] ${
            labelTop && parentComponent == "editInvitation" && "text-cm3"
          }
            ${
              labelTop
                ? `-top-[9px]  ${styles.text_xxs} ${styles.texttoplabel}`
                : !placeholder?.length
                ? "top-1/4 text-sm"
                : "hidden"
            }
            ${disabled ? "text-cgy2" : "text-cgy3"}`}
          htmlFor={id}
          data-testid={`disabled_${label}`}
        >
          {label}
        </label>
      )}
      <NumberFormat
        data-testid={`formMask_${label}`}
        {...rest}
        id={id}
        placeholder={placeholder}
        customInput={Input}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={`${disabled ? "text-cgy2" : "text-cgy4"}`}
        getInputRef={innerRef}
      />
      <span
        className={`
        ${styles.text_xxs} absolute left-3 -bottom-[18px] truncate font-semibold
        ${error && "text-[#E32b25]"}`}
        data-testid={`${label}_err_msg`}
      >
        {error || message}
      </span>
    </div>
  );
};
