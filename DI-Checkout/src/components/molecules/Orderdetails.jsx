export const Orderdetails = (props) => {
  const { isorder } = props;
  const Orderdetail = () => {
    return (
      <div className="flex justify-between">
        <p className="font-normal">{props.title}</p>
        <p className="font-medium">{props.value}</p>
      </div>
    );
  };
  const Orderbill = () => {
    return (
      <div className="flex justify-between border-b border-gray-500 pb-3 mb-3">
        <p className="font-semibold">{props.title}</p>
        <p>
          {props.value !== "FREE" && "€"}
          {props.value}
        </p>
      </div>
    );
  };

  return isorder ? Orderbill() : Orderdetail();
};
