import { useState } from "react";
import ChevronArrow from "../../../../public/assets/chevron_arrow.svg";
import HeaderIcon from "../../../../public/assets/Dine-in-icon.svg";
import Image from "next/image";
import SubDetails from "@/components/atoms/SubDetails/SubDetails";

export default function Header({ orderData }) {
  const [openAccordion, setOpenAccordion] = useState(false);
  const handleAccordionClick = () => {
    setOpenAccordion(!openAccordion);
  };

  return (
    <div className="border-b-[12px] h-fit border-[#D8E2F0]">
      <div
        className="h-[56px] bg-[#FFFFFF] flex justify-between items-center py-3 px-6 cursor-pointer"
        onClick={handleAccordionClick}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-[10px]">
            <span className="w-[34px] h-7">
              <Image src={HeaderIcon} alt="Icon" />
            </span>
            <p className="text-[#404040] font-semibold text-base leading-5">
              {orderData?.orderType == "Dine-In"
                ? "Dine In Order"
                : orderData?.orderType == "To-Go"
                ? "To Go Order"
                : orderData?.orderType?.startsWith("VIP")
                ? "VIP Order"
                : ""}
            </p>
          </div>
          <p className="text-[#404040] font-semibold text-base leading-5">
            {orderData?.currencySymbol}
            {Number(orderData?.total).toFixed(2)}
          </p>
        </div>
        <span
          className={`h-6 w-4 ml-[10px] flex items-center transition-transform duration-300 ease-in-out ${
            openAccordion && "scale-y-[-1]"
          }`}
        >
          <Image src={ChevronArrow} alt="Icon" width={16} height={16} />
        </span>
      </div>
      <div
        className={`bg-[#FFFFFF] w-full py-3 px-6 transition-transform duration-200 ease-in-out ${
          openAccordion ? "block" : "hidden"
        }`}
      >
        <div className="border-y-2 border-[#D8E2F0] mb-3">
          {orderData?.orderType == "Dine-In" && (
            <>
              {orderData?.orderData?.map((each, i) => {
                return (
                  <div key={i} className="flex flex-col py-2 gap-4">
                    <div className="flex justify-between w-full">
                      <div className="flex flex-col justify-start w-[75%]">
                        <p className="text-[#404040] font-semibold text-base leading-5 ">
                          {each?.itemName}
                        </p>
                        <p className="text-[#404040] font-normal text-sm leading-5">
                          Quantity: {each?.quantity}
                        </p>
                        <p className="text-[#404040] font-normal text-sm leading-5 w-3/4 capitalize">
                          {each?.modifiersData.join(", ")}
                        </p>
                      </div>
                      <p className="text-[#404040] font-normal text-sm leading-5">
                        {orderData?.currencySymbol}
                        {Number(each?.price).toFixed(2)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </>
          )}
          {orderData?.orderType == "To-Go" && (
            <>
              {orderData?.orderData?.map((each, i) => {
                return (
                  <div key={i} className="flex flex-col py-2 gap-4">
                    <div className="flex justify-between w-full">
                      <div className="flex flex-col justify-start w-[75%]">
                        <p className="text-[#404040] font-semibold text-base leading-5 ">
                          {each?.itemName}
                        </p>
                        <p className="text-[#404040] font-normal text-sm leading-5">
                          Quantity: {each?.quantity}
                        </p>
                        <p className="text-[#404040] font-normal text-sm leading-5 w-3/4 capitalize">
                          {each?.modifiersData.join(", ")}
                        </p>
                      </div>
                      <p className="text-[#404040] font-normal text-sm leading-5">
                        {orderData?.currencySymbol}
                        {Number(each?.price).toFixed(2)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </>
          )}
          {orderData?.orderType == "VIPSingle" && (
            <div className="flex flex-col py-2 gap-4">
              <div className="flex justify-between w-full">
                <div className="flex flex-col justify-start w-[75%]">
                  <p className="text-[#404040] font-semibold text-base leading-5 ">
                    {orderData?.fullName}
                  </p>
                </div>
                <p className="text-[#404040] font-normal text-sm leading-5">
                  {orderData?.currencySymbol}
                  {Number(orderData?.subTotal).toFixed(2)}
                </p>
              </div>
            </div>
          )}
          {orderData?.orderType == "VIPBulk" && (
            <>
              {orderData?.bulkOrderDetails?.map((each, i) => {
                return (
                  <div key={i} className="flex flex-col py-2 gap-4">
                    <div className="flex justify-between w-full">
                      <div className="flex flex-col justify-start w-[75%]">
                        <p className="text-[#404040] font-semibold text-base leading-5 ">
                          {each?.fullName}
                        </p>
                      </div>
                      <p className="text-[#404040] font-normal text-sm leading-5">
                        {orderData?.currencySymbol}
                        {Number(each?.price).toFixed(2)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </>
          )}
        </div>
        <div className="flex flex-col justify-between w-full">
          <SubDetails
            itemName="Subtotal"
            itemValue={`${orderData?.currencySymbol}${Number(
              orderData?.subTotal
            ).toFixed(2)}`}
          />
          {/* {orderData?.orderType !== "Dine-In" && (
            <SubDetails
              itemName="Shipping"
              itemValue={
                Number(orderData?.shipping) == 0
                  ? "FREE"
                  : `${orderData?.currencySymbol}${Number(
                      orderData?.shipping
                    ).toFixed(2)}`
              }
            />
          )} */}
          {!orderData?.orderType?.startsWith("VIP") && (
            <SubDetails
              itemName="Discount"
              itemValue={`${orderData?.currencySymbol}${Number(
                orderData?.discount
              ).toFixed(2)}`}
            />
          )}
          <SubDetails
            itemName="Tax"
            itemValue={`${orderData?.currencySymbol}${Number(
              orderData?.tax
            ).toFixed(2)}`}
          />
          <SubDetails
            itemName="Tip"
            itemValue={`${orderData?.currencySymbol}${Number(
              orderData?.tip
            ).toFixed(2)}`}
          />
          <div className="mt-2 flex justify-between w-full">
            <p className="text-[#404040] font-bold text-base leading-5">
              Order Total
            </p>
            <p className="text-[#404040] font-bold text-base leading-5">
              {orderData?.currencySymbol}
              {Number(orderData?.total).toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
