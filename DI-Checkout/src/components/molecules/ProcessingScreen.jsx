import Image from "next/image";

export default function ProcessingScreen(){
  return (
    <div className="flex justify-center bg-black">
      <div className="flex flex-col items-center justify-center h-screen bg-[#ffffff] w-[400px] cur">
        <div className="loader ease-linear rounded-full border-8 border-t-8 bg-[#ffffff] h-10 w-10 mb-4"></div>
        <div className="flex flex-col text-center text-[#404040]">
          <p className="text-2xl font-semibold  leading-8">
            Processing your payment
          </p>
          <p className="font-normal leading-[22px] mt-2">
            Please stay on this screen while we process your payment
          </p>
          <div className="flex flex-col gap-4 mt-56 self-center">
            <Image
              className="ml-28 mt-16 "
              src="/assets/Vector.svg"
              alt="Lock"
              width={20}
              height={20}
            />
            <p className="text-[#767676] font-normal text-center">
              Secure checkout powered by NEMS
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
