"use client";
import Image from "next/image";
import Mastercard from "../../../../public/assets/Mastercard.svg";
import Visa from "../../../../public/assets/Visa.svg";
import Amex from "../../../../public/assets/Amex.svg";
import Discover from "../../../../public/assets/Discover.svg";
import Card from "../../../../public/assets/card.svg";

export default function CardImage({value, ...props }) {
  return (
    <Image
      src={
        validateCardType(value?.replace(/\s+/g, "")) === "amex"
          ? Amex
          : validateCardType(value?.replace(/\s+/g, "")) === "discover"
          ? Discover
          : validateCardType(value?.replace(/\s+/g, "")) === "mastercard"
          ? Mastercard
          : validateCardType(value?.replace(/\s+/g, "")) === "visa"
          ? Visa
          : Card
      }
      alt="Card"
      {...props}
    />
  );
}

export function validateCardType(cardNumber) {
  const visaRegex = /^4[0-9]{12}(?:[0-9]{3})?$/;
  const amexRegex = /^3[47][0-9]{13}$/;
  const masterCardRegex = /^(?:5[1-5][0-9]{14}|2[2-7][0-9]{14})$/;
  const discoverRegex =
    /^6(?:011|5[0-9]{2}|4[4-9][0-9]|22[1-9][0-9]|22[2-8][0-9]|229[0-5])[0-9]{12}$/;

  if (visaRegex.test(cardNumber)) {
    return "visa";
  } else if (amexRegex.test(cardNumber)) {
    return "amex";
  } else if (masterCardRegex.test(cardNumber)) {
    return "mastercard";
  } else if (discoverRegex.test(cardNumber)) {
    return "discover";
  } else {
    return "invalid";
  }
}
