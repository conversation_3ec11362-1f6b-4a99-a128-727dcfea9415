export const Button = ({ value, handleClick, isChecked, type }) => {
  return (
    <button
      type={type ? type : "button"}
      className={` bg-[#00A6A2] text-[#FFFFFF] font-bold text-base leading-[22px] py-2 h-auto w-full rounded-full ${
        !isChecked &&
        "opacity-50 cursor-not-allowed bg-[#E9E9E9] text-[#949494]"
      } transition duration-300 `}
      disabled={!isChecked}
      onClick={() => handleClick()}
    >
      {value}
    </button>
  );
};
