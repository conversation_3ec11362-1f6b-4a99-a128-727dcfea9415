import { Field } from "formik";

export default function InputField  ({ errors, touched, title, placeholder, value, name })  {
  return (
    <div className="mb-4">
      <label htmlFor={name} className="block">
        {title}
      </label>
      <Field
        type="text"
        id={name}
        name={name}
        className={`w-full border rounded-md py-2 px-3 ${
          errors[name] && touched[name] && !value ? "border-red-500" : ""
        }`}
        placeholder={placeholder}
        value={value}
      />
      {errors[name] && touched[name] && !value && (
        <div className="text-red-500">Please enter {title.toLowerCase()}.</div>
      )}
    </div>
  );
};


