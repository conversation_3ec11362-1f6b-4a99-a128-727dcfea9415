.form_input {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // padding: 0;
  // margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #404040;
  font-family: "Open Sans", sans-serif;
  outline: none;
  border: none;
  background-color: transparent;
  transition: all 0.1s ease;
  // padding-left: 36px;

  &:disabled {
    @media only screen and (-webkit-min-device-pixel-ratio: 0) {
      color: #767676;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 0.75) {
      color: #767676;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 1) {
      color: #767676;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 1.5) {
      color: #767676;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 2) {
      color: #767676;
    }
    color: #767676;
    pointer-events: none;
  }
}

.form_chargeBack {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #404040;
  font-family: "Open Sans", sans-serif;
  outline: none;
  border: none;
  background-color: transparent;
  transition: all 0.1s ease;
  text-align: right;

  &:disabled {
    @media only screen and (-webkit-min-device-pixel-ratio: 0) {
      color: #adadad;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 0.75) {
      color: #adadad;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 1) {
      color: #adadad;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 1.5) {
      color: #adadad;
    }
    @media only screen and (-webkit-min-device-pixel-ratio: 2) {
      color: #adadad;
    }
    color: #adadad;
    pointer-events: none;
  }
}

input.form-input:-webkit-autofill,
input.form-input:-webkit-autofill:hover,
input.form-input:-webkit-autofill:focus,
input.form-input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

textarea.form-input {
  padding: 8px;
}

input.form-input::placeholder {
  color: #e8e8e8;
}
