"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "@/components/atoms/Button";
import Footer from "@/components/molecules/Footer/Footer";
import Header from "@/components/molecules/Header/Header";
import AppHeader from "@/components/molecules/AppHeader/AppHeader";
import { Summarydetails } from "@/components/molecules/Summarydetails";
import SubDetails from "@/components/atoms/SubDetails/SubDetails";
import SummaryTitle from "@/components/atoms/SummaryTitle/SummaryTitle";
import "./PaymentConfirmation.styles.scss";
import ProcessingScreen from "@/components/molecules/ProcessingScreen";
import { callAPI } from "@/app/payment/page";
import { OrderUpdatedModal } from "@/components/molecules/OrderUpdatedModal/OrderUpdatedModal";
import { validateCardType } from "@/components/molecules/CardImage/CardImage";

export default function PaymentConfirmation() {
  const router = useRouter();
  // const [orderData, setOrderData] = useState();
  const [paymentData, setPaymentData] = useState();
  const [headerData, setHeaderData] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  let apiUrl;

  useEffect(() => {
    const sessionData = JSON.parse(
      window.sessionStorage && sessionStorage.getItem("orderData")
    );
    if (typeof window !== "undefined") {
      setPaymentData(
        JSON.parse(window.sessionStorage && sessionStorage.getItem("data"))
      );
      setHeaderData(sessionData);
      if (
        sessionData?.orderType === "dineIn" ||
        sessionData?.orderType === "To-Go"
      ) {
        apiUrl = `${process.env.NEXT_PUBLIC_ORDER_MANAGEMENT_DOMAIN}/public/api/order/get-by-id/${sessionData?.orderId}`;
      }
      if (sessionData?.orderType === "VIPSingle") {
        apiUrl = `${process.env.NEXT_PUBLIC_ORDER_MANAGEMENT_DOMAIN}/public/api/order/entertainer/order/${sessionData?.orderId}`;
      }
      if (sessionData?.orderType === "VIPBulk") {
        apiUrl = `${process.env.NEXT_PUBLIC_ORDER_MANAGEMENT_DOMAIN}/public/api/order/entertainer/order/bulk/${sessionData?.orderId}`;
      }
    }
  }, []);

  const contactInfo = {
    email: paymentData?.email,
  };
  const shippingAddress = {
    name: paymentData?.firstName + " " + paymentData?.lastName,
    addressOne: paymentData?.addressOne,
    addressTwo: paymentData?.addressTwo,
    state: paymentData?.city,
  };
  const paymentInfo = {
    cardNumber: paymentData?.cardNumber,
    name: paymentData?.cardName,
    state: paymentData?.city,
    city: paymentData?.country,
  };
  const [isChecked, setIsChecked] = useState(false);

  const handleIsChecked = () => {
    setIsChecked(!isChecked);
  };

  const handlePlaceOrder = async () => {
    const recheckData = await callAPI("GET", paymentData?.apiURL);
    const orderChanged =
      (headerData?.orderType === "VIPSingle"
        ? parseFloat(recheckData?.results[0]?.remaining_balance)
        : parseFloat(recheckData?.results?.remaining_balance)) !==
      parseFloat(headerData?.total) -
        (paymentData?.paidTip ? 0 : parseFloat(headerData?.tip));
    if (orderChanged) {
      return setShowConfirmation(true);
    }
    const payload = {
      transaction_type: "card",
      payment_for: "dine-in",
      merchant_id:Number(headerData?.apiParams.merchant_id),
      tip: paymentData?.paidTip ? 0 : parseFloat(headerData?.tip),
      reference: headerData?.orderId.toString(),
      is_split_transaction: true,
      amount:
        Number(headerData?.total) -
        (paymentData?.paidTip ? 0 : parseFloat(headerData?.tip)),
      card_type: validateCardType(paymentData?.cardNumber.replace(/\s+/g, "")),
      teu_request: {
        card_number: paymentData?.cardNumber.replace(/\s+/g, ""),
        card_expiry_month: paymentData?.expiryDate.slice(0, 2),
        card_expiry_year: `20${paymentData?.expiryDate.slice(3, 5)}`,
        card_cvv2: paymentData?.CVV,
        first_name: paymentData?.firstName,
        last_name: paymentData?.lastName,
        street_address_1: paymentData?.addressOne,
        city: paymentData?.city,
        zip: paymentData?.zip,
        // country: paymentData?.country,
        country: "BG",
        email_address: paymentData?.email,
      },
    };
    const vipSinglePayload = {
      transaction_type: "card",
      payment_for: "vip",
      merchant_id:Number(headerData?.apiParams.merchant_id),
      tip: Number(headerData?.tip),
      reference: headerData?.orderId.toString(),
      is_split_transaction: true,
      is_bulk_payment: false,
      amount:
        Number(headerData?.total) -
        (paymentData?.paidTip ? 0 : parseFloat(headerData?.tip)),
      card_type: validateCardType(paymentData?.cardNumber.replace(/\s+/g, "")),
      teu_request: {
        card_number: paymentData?.cardNumber.replace(/\s+/g, ""),
        card_expiry_month: paymentData?.expiryDate.slice(0, 2),
        card_expiry_year: `20${paymentData?.expiryDate.slice(3, 5)}`,
        card_cvv2: paymentData?.CVV,
        first_name: paymentData?.firstName,
        last_name: paymentData?.lastName,
        street_address_1: paymentData?.addressOne,
        city: paymentData?.city,
        zip: paymentData?.zip,
        // country: paymentData?.country,
        country: "BG",
        email_address: paymentData?.email,
      },
    };

    const vipBulkPayload = {
      transaction_type: "card",
      payment_for: "vip",
      merchant_id: Number(headerData?.apiParams.merchant_id),
      tip: Number(headerData?.tip),
      reference: headerData?.orderId.toString(),
      is_split_transaction: true,
      is_bulk_payment: true,
      amount:
        Number(headerData?.total) -
        (paymentData?.paidTip ? 0 : parseFloat(headerData?.tip)),
      card_type: validateCardType(paymentData?.cardNumber.replace(/\s+/g, "")),
      teu_request: {
        card_number: paymentData?.cardNumber.replace(/\s+/g, ""),
        card_expiry_month: paymentData?.expiryDate.slice(0, 2),
        card_expiry_year: `20${paymentData?.expiryDate.slice(3, 5)}`,
        card_cvv2: paymentData?.CVV,
        first_name: paymentData?.firstName,
        last_name: paymentData?.lastName,
        street_address_1: paymentData?.addressOne,
        city: paymentData?.city,
        zip: paymentData?.zip,
        // country: paymentData?.country,
        country: "BG",
        email_address: paymentData?.email,
      },
    };
    setIsLoading(true);
    const data = await callAPI(
      "POST",
      `${process.env.NEXT_PUBLIC_ORDER_MANAGEMENT_DOMAIN}/public/api/order/payment`,
      headerData?.orderType == "Dine-In" || headerData?.orderType == "To-Go"
        ? payload
        : headerData?.orderType == "VIPSingle"
        ? vipSinglePayload
        : vipBulkPayload
    );
    if (data?.results) {
      setIsLoading(false);
      router.push("/order-successful");
      sessionStorage.clear("data");
      sessionStorage.clear("orderData");
    }
    if (data?.error) {
      setIsLoading(false);
      router.push(
        `/payment-unsuccessful?error=${JSON.stringify(data?.details)}`
      );
      // sessionStorage.clear("data");
      sessionStorage.clear("orderData");
    }
  };

  const handleBackClick = () => {
    const { merchant_id, orderId, amount, currency, orderType } =
      headerData.apiParams;
    router.push(
      `/payment?orderId=${orderId}&amount=${amount}&orderType=${orderType}&currency=${currency}&merchant_id=${merchant_id}`
    );
  };

  return (
    <>
      {isLoading ? (
        <ProcessingScreen />
      ) : (
        <div className="min-h-screen bg-black flex justify-center">
          <div className="max-w-[400px] w-full min-h-full bg-[#FFFFFF] ">
            <div className="shadow-sm w-full sticky top-0 z-20">
              <AppHeader />
              <Header orderData={headerData} />
            </div>
            <div className="p-8">
              <Summarydetails
                obj={contactInfo}
                title="Contact Information"
                isPaymentInfo={false}
              />
              <Summarydetails obj={shippingAddress} title="Details" />
              <Summarydetails obj={paymentInfo} title="Payment" isPaymentInfo />
              <SummaryTitle title="Order Summary" />
              <div className="flex flex-col justify-between w-full">
                <SubDetails
                  itemName="Subtotal"
                  itemValue={`${headerData?.currencySymbol}${Number(
                    headerData?.subTotal
                  ).toFixed(2)}`}
                />
                {/* {headerData?.orderType !== "Dine-In" && (
                  <SubDetails
                    itemName="Shipping"
                    itemValue={
                      Number(headerData?.shipping) == 0
                        ? "FREE"
                        : `${headerData?.currencySymbol}${Number(
                            headerData?.shipping
                          ).toFixed(2)}`
                    }
                  />
                )} */}
                {!headerData?.orderType?.startsWith("VIP") && (
                  <SubDetails
                    itemName="Discount"
                    itemValue={`${headerData?.currencySymbol}${Number(
                      headerData?.discount
                    ).toFixed(2)}`}
                  />
                )}
                <SubDetails
                  itemName="Tax"
                  itemValue={`${headerData?.currencySymbol}${Number(
                    headerData?.tax
                  ).toFixed(2)}`}
                />
                <SubDetails
                  itemName="Tip"
                  itemValue={`${headerData?.currencySymbol}${Number(
                    headerData?.tip
                  ).toFixed(2)}`}
                />
                <div className="mt-2 flex justify-between w-full mb-[35px]">
                  <h1 className="text-[#404040] font-bold text-base leading-5">
                    Order Total
                  </h1>
                  <span className="text-[#404040] font-bold text-base leading-5">
                    {headerData?.currencySymbol}
                    {headerData?.total?.toFixed(2)}
                  </span>
                </div>
              </div>
              <div className="flex flex-col mb-4">
                <div className="mb-4 align-top">
                  <input
                    type="checkbox"
                    name="terms"
                    value="terms"
                    onChange={handleIsChecked}
                    className="custom-checkbox"
                  />
                  <label className="text-sm font-normal leading-5">
                    {" "}
                    By clicking the pay now button, you confirm that you have
                    read and agree to the{" "}
                    <span className="underline text-[#00A6A2] font-semibold cursor-pointer">
                      Terms of Services
                    </span>{" "}
                    and{" "}
                    <span className="underline text-[#00A6A2] font-semibold cursor-pointer">
                      Privacy Policy
                    </span>
                  </label>
                </div>
                <Button
                  isChecked={isChecked}
                  handleClick={handlePlaceOrder}
                  value="Pay Now"
                />
                <div className="text-center">
                  <p
                    className="text-xs text-[#00A6A2] font-semibold mt-4 cursor-pointer leading-[17px] hover:underline"
                    onClick={handleBackClick}
                  >
                    Back
                  </p>
                </div>
              </div>
            </div>
            <Footer />
          </div>
          <OrderUpdatedModal
            isOpen={showConfirmation}
            onClose={() => setShowConfirmation(false)}
            handleProceed={handleBackClick}
          />
        </div>
      )}
    </>
  );
}
