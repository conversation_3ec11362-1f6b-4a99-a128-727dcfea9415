/* Hide the default checkbox */
.custom-checkbox {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #00a6a2;
  border-radius: 4px;
  outline: none;
  transition: background-color 0.3s;
  position: relative;
  cursor: pointer;
}

/* Style the custom checkbox when checked */
.custom-checkbox:checked {
  background-color: #00a6a2;
}

/* Add a checkmark using pseudo-element */
.custom-checkbox:checked::after {
  content: "";
  position: absolute;
  top: 0px;
  left: 4px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
