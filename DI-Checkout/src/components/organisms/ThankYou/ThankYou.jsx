"use client";
import PrintButton from "@/components/atoms/PrintButton/PrintButton";
import { Orderdetails } from "@/components/molecules/Orderdetails";

export default function Thankyou() {
  const shippingAddress = {
    address: "123 Easy Breezy Way",
    state: "San Diego Ca 90019",
    phone: "(*************",
    email: "<EMAIL>",
  };
  return (
    <div className="min-h-screen flex justify-center bg-black">
      <div className="flex flex-col items-center justify-center px-2 w-[400px] bg-[#ffffff]">
        <p className="text-5xl font-bold mb-4 px-2 mt-3">
          Thank you for your order
        </p>
        <div className="border border-gray-500 p-4 h-[auto] flex flex-col rounded-[2px]">
          <Orderdetails title="Order Number" value="V4018025892" />
          <Orderdetails title="Order Date" value="Thu Jun 06 2024" />
          <Orderdetails title="Customer" value="John Doe" />
          {/* COMMENTED AS THERE IS NO NEED OF PRINT FEATURE BUTTON */}
          {/* <div className="flex justify-center mt-3 mb-3">
            <PrintButton value="PRINT" />
          </div> */}
          <p className="mt-6">
            Please keep the above numbers for your reference. We&apos;ll also
            send a confirmation to the email address you used for this order.
            Please allow up to 24 hours for us to process your order for
            shipment
          </p>
          <p className="text-xl font-bold mb-2 mt-2">Shipping Address</p>
          {shippingAddress && (
            <div className="flex flex-col">
              <p className="font-semibold">Jane Doe</p>
              {Object.keys(shippingAddress).map((key) => (
                <Orderdetails key={key} title={shippingAddress[key]} />
              ))}
            </div>
          )}
        </div>

        <div className="mt-2 border border-gray-500 p-4 h-[auto] w-full flex flex-col">
          <h1 className="text-xl font-bold mb-4">Order Summary</h1>
          <Orderdetails title="SUBTOTAL" value="214.99" isorder />
          <Orderdetails title="SHIPPING" value="FREE" isorder />
          <Orderdetails title="ESTIMATED TAX" value="16.67" isorder />
          <Orderdetails title="ORDER TOTAL" value="231.66" isorder />
        </div>
      </div>
    </div>
  );
}
