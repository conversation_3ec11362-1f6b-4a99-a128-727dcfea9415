"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/atoms/Button";
import Union from "../../../../public/assets/Union.svg";
import { useRouter, useSearchParams } from "next/navigation";

export default function PaymentUnsuccessful() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const error = searchParams.get("error");

  const [headerData, setHeaderData] = useState({});
  useEffect(() => {
    if (typeof window !== "undefined") {
      setHeaderData(
        window.sessionStorage &&
          JSON.parse(sessionStorage?.getItem("orderData"))
      );
    }
  }, []);

  const handleTryAgain = () => {
    const { merchant_id, orderId, amount, currency, orderType } =
      headerData.apiParams;
    router.push(
      `/payment?orderId=${orderId}&amount=${amount}&orderType=${orderType}&currency=${currency}&merchant_id=${merchant_id}`
    );
  };
  return (
    <div className="flex justify-center bg-black ">
      <div className="min-h-screen bg-[#FFFFFF] w-[400px] px-10">
        <div className="text-center h-[542px] flex flex-col justify-center items-center">
          <div className="flex justify-center">
            <Image src={Union} alt="Union" className="w-[50px] h-12" />
          </div>
          <div className="text-[#404040]">
            <h1 className="text-2xl font-semibold mt-4 leading-8 ">
              Payment unsuccessful
            </h1>
            <p className="text-[16px] font-normal leading-[22px] mt-2">
              <span className="italic">{error ? error : "Not Found"},</span>{" "}
              please try again.
            </p>
          </div>

          <div className="py-4 w-full ">
            <Button
              value="Try Again"
              isChecked={true}
              handleClick={handleTryAgain}
            />
          </div>
        </div>
        <div className="flex flex-col justify-center items-center gap-4">
          <Image src="/assets/Vector.svg" alt="Lock" width={20} height={20} />
          <p className="text-xs text-[#767676] font-normal text-center">
            Secure checkout powered by NEMS
          </p>
        </div>
      </div>
    </div>
  );
}
