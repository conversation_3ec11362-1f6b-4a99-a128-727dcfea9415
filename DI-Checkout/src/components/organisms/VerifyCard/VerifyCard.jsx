"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import SummaryTitle from "@/components/atoms/SummaryTitle/SummaryTitle";
import Image from "next/image";
import verifycard from "../../../../public/assets/verifycard.svg";
import { Button } from "@/components/atoms/Button";

import ProcessingScreen from "@/components/molecules/ProcessingScreen";
import CardVerified from "./CardVerified/CardVerified";

export default function VerifyCard({ title }) {
  const [verifying, setVerifying] = useState(false);
  const [verificationComplete, setVerificationComplete] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showCardVerified, setShowCardVerified] = useState(false);

  const router = useRouter();

  useEffect(() => {
    const delay = setTimeout(() => {
      setIsLoading(false);
    }, 2000);
    return () => clearTimeout(delay);
  }, []);

  const handleVerify = () => {
    setVerifying(true);

    setTimeout(() => {
      setVerificationComplete(true);
      setShowCardVerified(true);

      setTimeout(() => {
        setIsLoading(true);
        setTimeout(() => {
          router.push("/order-successful");
        }, 2000);
      }, 2000);
    }, 2000);
  };

  if (isLoading) {
    return <ProcessingScreen />;
  }

  return (
    <div className="min-h-screen bg-black flex justify-center">
      <div className="max-w-[400px] w-full min-h-full bg-[#FFFFFF] p-8">
        <SummaryTitle title="Verify your Card" />

        <div className="text-center mt-6">
          <div className="flex justify-center">
            <span className="w-[84px] h-[98px]">
              <Image src={verifycard} alt="verifycard" />
            </span>
          </div>
        </div>

        {showCardVerified && <CardVerified />}

        {!verificationComplete && !showCardVerified && (
          <>
            <p className="text-[14px] text-[#404040] font-normal leading-[19px] mb-1 mt-6">
              To protect you from fraud, follow these steps to verify your card:
            </p>
            <div className="ml-6">
              <ul className="list-decimal">
                <li className="text-[14px] text-[#404040] font-normal leading-[19px] mb-1 mt-6">
                  Check your card statement for a temporary charge from
                  <span className="text-[14px] text-[#404040] font-bold leading-[19px] mb-1">
                    {" "}
                    [Descriptor Name].{" "}
                  </span>
                </li>
                <li className="text-[14px] text-[#404040] font-normal leading-[19px] mb-1 ">
                  Entering the exact amount of the charge.
                </li>
              </ul>
            </div>
            <p className="font-normal text-[14px] leading-[19px] text-[#767676] mt-2">
              Note: This charge will be removed.
            </p>

            <div className="mt-8">
              <p className="font-normal text-[16px] leading-[22px] text-[#404040]">
                Amount
              </p>

              <div className="mt-3">
                <div className="flex gap-2 items-center p-[9px] border rounded-md border-[#CCCCCC] w-full h-[46px]">
                  <span className="text-[#00A6A2] font-bold">$</span>
                  <span className="h-[27px] w-px bg-[#CCCCCC]"></span>

                  <input
                    type="text"
                    className="w-full h-full border-none focus:outline-none font-semibold text-[16px] leading-[22px] text-[#404040]"
                    value="1.15"
                  />
                </div>
              </div>
            </div>

            <div className="mt-8">
              {verifying ? (
                <div className="flex items-center">
                  <p className="font-semibold text-[16px] leading-[22px] text-[#404040] ml-28">
                    Verifying
                  </p>
                  <div className="loaderone ml-2"></div>
                </div>
              ) : (
                <Button
                  handleClick={handleVerify}
                  value="Verify"
                  isChecked={true}
                />
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
