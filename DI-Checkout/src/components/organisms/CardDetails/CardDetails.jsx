"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Button } from "@/components/atoms/Button";
import {
  FormCard,
  FormField,
  FormMask,
} from "@/components/molecules/FormCard/FormCard";
import Header from "@/components/molecules/Header/Header";
import Footer from "@/components/molecules/Footer/Footer";
import AppHeader from "@/components/molecules/AppHeader/AppHeader";
import Image from "next/image";
import Vector from "../../../../public/assets/Vector.svg";
import CloseIcon from "../../../../public/assets/Close_Icon.svg";
import { callAPI } from "@/app/payment/page";

export default function CardDetails() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderType = searchParams.get("orderType");
  const orderId = searchParams.get("orderId") ?? 0;
  const currencySymbol = searchParams.get("currency") ?? "€";
  const amount = searchParams.get("amount") ?? 0;
  const merchant_id = searchParams.get("merchant_id") ?? 0;

  const [url, setUrl] = useState();
  const [data, setData] = useState();
  const [formData, setFormData] = useState();
  const [selectedTipId, setSelectedTipId] = useState(0);
  const [customTip, setCustomTip] = useState(false);
  const [customTipAmount, setCustomTipAmount] = useState();
  const [customTipError, setCustomTipError] = useState();

  const [toGoData, setToGoData] = useState({});
  const [dineInnData, setDineInnData] = useState({});

  let apiUrl;

  useEffect(() => {
    if (orderType === "dineIn") {
      apiUrl = `${process.env.NEXT_PUBLIC_ORDER_MANAGEMENT_DOMAIN}/public/api/order/get-by-id/${orderId}`;
    }
    if (orderType === "VIPSingle") {
      apiUrl = `${process.env.NEXT_PUBLIC_ORDER_MANAGEMENT_DOMAIN}/public/api/order/entertainer/order/${orderId}`;
    }
    if (orderType === "VIPBulk") {
      apiUrl = `${process.env.NEXT_PUBLIC_ORDER_MANAGEMENT_DOMAIN}/public/api/order/entertainer/order/bulk/${orderId}`;
    }
    setUrl(apiUrl);
  }, [orderType, orderId]);

  const tipAmounts = [
    { id: 1, amount: amount / 10, percentage: "10%" },
    { id: 2, amount: (amount * 3) / 20, percentage: "15%" },
    { id: 3, amount: amount / 5, percentage: "20%" },
  ];

  const containsEmoji = (text) => {
    const emojiPattern =
      /[\u{1F300}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;
    return emojiPattern.test(text);
  };

  const visaRegex = /^4[0-9]{12}(?:[0-9]{3})?$/;
  const amexRegex = /^3[47][0-9]{13}$/;
  const masterCardRegex = /^(?:5[1-5][0-9]{14}|2[2-7][0-9]{14})$/;
  const discoverRegex =
    /^6(?:011|5[0-9]{2}|4[4-9][0-9]|22[1-9][0-9]|22[2-8][0-9]|229[0-5])[0-9]{12}$/;

  const validationSchema = Yup.object().shape({
    firstName: Yup.string()
      // .trim()
      .required("First Name is required")
      .test(
        "no-leading-spaces",
        "First Name cannot start with a space",
        (value) => value && value[0] !== " "
      )
      .test("no-emojis", "First Name cannot contain emojis", (val) => {
        return !containsEmoji(val);
      })
      .max(32, "First Name cannot be more than 32 characters")
      .matches(
        /^[A-Za-z\s]+$/,
        "First Name cannot contain numbers or special characters"
      ),
    lastName: Yup.string()
      // .trim()
      .required("Last Name is required")
      .test(
        "no-leading-spaces",
        "Last Name cannot start with a space",
        (value) => value && value[0] !== " "
      )
      .test("no-emojis", "Last Name cannot contain emojis", (val) => {
        return !containsEmoji(val);
      })
      .max(32, "Last Name cannot be more than 32 characters"),
    addressOne: Yup.string()
      .required("Address Line Required")
      .max(64, "Address Line cannot be more than 64 characters")
      .test("no-emojis", "Address cannot contain emojis", (val) => {
        return !containsEmoji(val);
      }),
    city: Yup.string()
      .required("City Required")
      .max(64, "Max 64 characters allowed")
      .test("no-emojis", "City cannot contain emojis", (val) => {
        return !containsEmoji(val);
      }),
    zip: Yup.string()
      .required("Zipcode Required")
      .test("no-emojis", "Zip code cannot contain emojis", (val) => {
        return !containsEmoji(val);
      })
      .min(3, "Zip code must be at least 3 characters")
      .max(6, "Max 6 characters allowed")
      .matches(
        /^[a-zA-Z0-9]+$/,
        "Zip code can only contain letters and numbers"
      ),
    country: Yup.string()
      .required("Country Required")
      .max(64, "Max 64 characters allowed")
      .test("no-emojis", "Country cannot contain emojis", (val) => {
        return !containsEmoji(val);
      }),
    addressTwo: Yup.string()
      .max(64, "Address Line cannot be more than 64 characters")
      .test("no-emojis", "Address cannot contain emojis", (val) => {
        return !containsEmoji(val);
      }),
    cardNumber: Yup.string()
      .required("Card number is required")
      .min(15, "Card number must be min 15 digits")
      // .matches(/^\d{4} \d{4} \d{4} \d{4}$/, "Card number must be min 15 digits")
      .test("is-valid-card", "Invalid card number", (value) => {
        if (!value) return false;
        const cardNumber = value.replace(/\s+/g, "");
        return (
          visaRegex.test(cardNumber) ||
          amexRegex.test(cardNumber) ||
          masterCardRegex.test(cardNumber) ||
          discoverRegex.test(cardNumber)
        );
      }),
    cardName: Yup.string()
      .required("Name on card is required")
      .matches(/^[A-Za-z ]+$/, "Name on card must contain only alphabets")
      .test("no-emojis", "Name cannot contain emojis", (val) => {
        return !containsEmoji(val);
      }),
    expiryDate: Yup.string()
      .required("Expiry date is required")
      .matches(/^(0[1-9]|1[0-2])\/\d{2}$/, "Expiry date must be in MM/YY")
      .test(
        "expiry-year",
        "Expiry date cannot be in the past",
        function (value) {
          if (!value) return false;
          const [month, year] = value.split("/").map(Number);
          const currentYear = new Date().getFullYear() % 100;
          const currentMonth = new Date().getMonth() + 1;
          if (year < currentYear) {
            return false;
          }
          if (year === currentYear && month < currentMonth) {
            return false;
          }
          return true;
        }
      ),
    CVV: Yup.string()
      .required("CVV is required")
      .matches(/^\d{3,4}$/, "CVV must be 3 or 4 digits"),
    email: Yup.string()
      .required("Email is required")
      .test("no-emojis", "Email cannot contain emojis", (val) => {
        return !containsEmoji(val);
      })
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.(com|org|net|edu|gov|mil|biz|info|mobi|name|aero|jobs|museum|in)$/,
        "Invalid email address"
      ),
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      setFormData(
        JSON.parse(window.sessionStorage && sessionStorage.getItem("data"))
      );
    }
  }, []);

  const formik = useFormik({
    initialValues: {
      firstName: formData?.firstName ?? "",
      lastName: formData?.lastName ?? "",
      addressOne: formData?.addressOne ?? "",
      addressTwo: formData?.addressTwo ?? "",
      city: formData?.city ?? "",
      zip: formData?.zip ?? "",
      country: formData?.country ?? "",
      cardNumber: formData?.cardNumber ?? "",
      cardName: formData?.cardName ?? "",
      expiryDate: formData?.expiryDate ?? "",
      CVV: formData?.CVV ?? "",
      email: formData?.email ?? "",
    },
    validationSchema: validationSchema,
    enableReinitialize: true,
  });

  const [tipAmount, setTipAmount] = useState(0);
  const [paidTip, setPaidTip] = useState(false);

  useEffect(() => {
    formData && formik.setValues(formData);
    setSelectedTipId(formData?.tipId ?? 0);
    setTipAmount(formData?.tipAmount ?? 0);
    setCustomTip(formData?.customTip ?? false);
    setCustomTipAmount(formData?.customTipAmount);
  }, [formData]);

  useEffect(() => {
    (async () => {
      try {
        const data = await callAPI("GET", apiUrl);
        setData(data);
        if (!formData || formData?.tipId === 0) {
          let tipAmount = 0;
          if (orderType === "VIPSingle") {
            tipAmount = parseFloat(data?.results[0]?.tip);
          } else {
            tipAmount = parseFloat(data?.results?.tip);
          }
          setTipAmount(tipAmount);
          if (tipAmount > 0) {
            setPaidTip(true);
          }
        }
      } catch (error) {
      }
    })();
  }, [apiUrl]);

  const handleTipSelection = (id) => {
    if (customTip) {
      setCustomTip(false);
      setCustomTipAmount();
    }
    if (selectedTipId === id) {
      setSelectedTipId(0);
      setTipAmount(0);
      return;
    } else {
      setSelectedTipId(id);
      setTipAmount(tipAmounts.find((tip) => tip.id === id)?.amount);
    }
  };

  useEffect(() => {
    if (data?.results?.order_type == "To-Go") {
      const orderData = data?.results?.orderItems?.map((each) => {
        const selectedModifiers =
          each?.selectedModifiers?.map((each) => {
            return each?.name;
          }) || [];
        const selectedOptions =
          each?.selectedOptions?.map((i) => i?.selectedOption?.option_name) ||
          [];
        return {
          itemName: each?.item_name,
          price: each?.price,
          quantity: each?.orderQuantity,
          modifiersData: [
            ...(selectedOptions ?? ""),
            ...(selectedModifiers ?? ""),
          ],
        };
      });

      setToGoData({
        orderType: data?.results?.order_type,
        orderId: data?.results?.id,
        subTotal: data?.results?.sub_total,
        shipping: data?.results?.shipping ?? "0.00",
        discount: data?.results?.discount,
        total:
          parseFloat(data?.results?.remaining_balance) +
          (paidTip ? 0 : tipAmount),
        tax: data?.results?.tax,
        tip: tipAmount,
        currencySymbol: currencySymbol,
        orderData: orderData,
        apiParams: {
          merchant_id: merchant_id,
          amount: amount,
          currency: currencySymbol,
          orderType: orderType,
          orderId: orderId,
        },
      });
    }

    if (data?.results?.order_type == "Dine-In") {
      const dineOrderData = data?.results?.orderItems?.map((items) => {
        const inner = items?.cartItems?.map((each) => {
          const selectedModifiers = each?.selectedModifiers?.map((each) => {
            return each?.name;
          });
          const selectedOptions = each?.selectedOptions?.map(
            (i) => i?.selectedOption?.option_name
          );
          return {
            itemName: each?.item_name,
            price: each?.price,
            quantity: each?.orderQuantity,
            modifiersData: [
              ...(selectedOptions ?? ""),
              ...(selectedModifiers ?? ""),
            ],
          };
        });
        return inner;
      });

      const flattenedOrderData = dineOrderData.flat().filter((item) => item);

      setDineInnData({
        orderType: data?.results?.order_type,
        orderId: data?.results?.id,
        subTotal: data?.results?.sub_total,
        shipping: data?.results?.shipping ?? "0.00",
        discount: data?.results?.discount,
        total:
          Number(data?.results?.remaining_balance) + (paidTip ? 0 : tipAmount),
        tax: data?.results?.tax,
        tip: tipAmount,
        currencySymbol: currencySymbol,
        orderData: flattenedOrderData,
        apiParams: {
          merchant_id: merchant_id,
          amount: amount,
          currency: currencySymbol,
          orderType: orderType,
          orderId: orderId,
        },
      });
    }
  }, [data, selectedTipId, tipAmount]);

  const vipSingleData = {
    orderType: orderType,
    orderId: data?.results ? data?.results[0]?.id : null,
    fullName: data?.results
      ? data?.results[0]?.first_name + " " + data?.results[0]?.last_name
      : null,
    total: data?.results
      ? Number(data?.results[0]?.remaining_balance) + (paidTip ? 0 : tipAmount)
      : null,
    subTotal:
      Number(data?.results ? data?.results[0]?.remaining_balance : 0) -
      Number(data?.results ? data?.results[0]?.tax : 0),
    discount: 0,
    tax: Number(data?.results ? data?.results[0]?.tax : 0),
    tip: tipAmount,
    currencySymbol: currencySymbol,
    shipping: data?.results?.shipping ?? "0.00",
    apiParams: {
      merchant_id: merchant_id,
      amount: amount,
      currency: currencySymbol,
      orderType: orderType,
      orderId: orderId,
    },
  };

  const bulkOrderDetails = data?.results?.entertainer_orders?.map((each) => {
    return {
      fullName: each?.first_name + " " + each?.last_name,
      price: each?.total_price,
    };
  });

  const vipBulkData = {
    orderType: orderType,
    orderId: data?.results?.id,
    total: Number(data?.results?.remaining_balance) + (paidTip ? 0 : tipAmount),
    subTotal:
      Number(data?.results?.remaining_balance) - Number(data?.results?.tax),
    discount: 0,
    tax: Number(data?.results?.tax),
    tip: tipAmount,
    currencySymbol: currencySymbol,
    shipping: data?.results?.shipping ?? "0.00",
    bulkOrderDetails: bulkOrderDetails,
    apiParams: {
      merchant_id: merchant_id,
      amount: amount,
      currency: currencySymbol,
      orderType: orderType,
      orderId: orderId,
    },
  };

  const handleSubmit = () => {
    sessionStorage.setItem(
      "data",
      JSON.stringify({
        firstName: formik.values.firstName,
        lastName: formik.values.lastName,
        addressOne: formik.values.addressOne,
        addressTwo: formik.values.addressTwo,
        city: formik.values.city,
        zip: formik.values.zip,
        country: formik.values.country,
        cardNumber: formik.values.cardNumber,
        cardName: formik.values.cardName,
        expiryDate: formik.values.expiryDate,
        CVV: formik.values.CVV,
        email: formik.values.email,
        tipId: selectedTipId,
        tipAmount: tipAmount,
        customTip: customTip,
        customTipAmount: customTipAmount,
        paidTip: paidTip,
        apiURL: url,
      })
    );

    data?.results?.order_type == "To-Go"
      ? sessionStorage.setItem("orderData", JSON.stringify(toGoData))
      : data?.results?.order_type == "Dine-In"
      ? sessionStorage.setItem("orderData", JSON.stringify(dineInnData))
      : orderType == "VIPSingle"
      ? sessionStorage.setItem("orderData", JSON.stringify(vipSingleData))
      : sessionStorage.setItem("orderData", JSON.stringify(vipBulkData));
    router.push("/payment-confirmation");
  };

  return (
    <div className="min-h-screen flex justify-center bg-[#0000000d]">
      <div className="max-w-[425px] bg-[#FFFFFF]">
        <div className="shadow-sm w-full sticky top-0 z-20">
          <AppHeader />
          <Header
            orderData={
              data?.results?.order_type == "To-Go"
                ? toGoData
                : data?.results?.order_type == "Dine-In"
                ? dineInnData
                : orderType == "VIPSingle"
                ? vipSingleData
                : vipBulkData
            }
          />
        </div>
        <div className="flex flex-col gap-2 mt-6 mx-6 pb-8 border-b-[1px] border-[#D8E2F0]">
          <div className="flex flex-col gap-[4px]">
            <h2 className="text-2xl font-semibold leading-8">Payment</h2>
            <div className="flex gap-[4px] items-center">
              <p className="text-base text-[#404040] leading-[22px]">
                All transactions are secured and encrypted.
              </p>
              <span className="h-[14px] w-[14px]">
                <Image src={Vector} alt="Icon" />
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-[11px] mt-2">
            <FormField
              label="First Name"
              id="first_name"
              parentComponent={"editInvitation"}
              {...formik.getFieldProps("firstName")}
              error={formik.touched.firstName && formik.errors.firstName}
            />
            <FormField
              label="Last Name"
              id="last_name"
              parentComponent={"editInvitation"}
              {...formik.getFieldProps("lastName")}
              error={formik.touched.lastName && formik.errors.lastName}
            />
            <FormField
              label="Address Line One"
              id="address_line_one"
              parentComponent={"editInvitation"}
              {...formik.getFieldProps("addressOne")}
              error={formik.touched.addressOne && formik.errors.addressOne}
            />
            <FormField
              label="Address Line Two (Optional)"
              id="address_line_two"
              parentComponent={"editInvitation"}
              {...formik.getFieldProps("addressTwo")}
              error={formik.touched.addressTwo && formik.errors.addressTwo}
            />
            <div className="flex gap-3 justify-between">
              <FormField
                label="City"
                id="city"
                parentComponent={"editInvitation"}
                {...formik.getFieldProps("city")}
                error={formik.touched.city && formik.errors.city}
              />
              <FormField
                label="Zip Code"
                id="zip_code"
                parentComponent={"editInvitation"}
                {...formik.getFieldProps("zip")}
                error={formik.touched.zip && formik.errors.zip}
              />
            </div>
            <FormField
              label="Country"
              id="country"
              parentComponent={"editInvitation"}
              {...formik.getFieldProps("country")}
              error={formik.touched.country && formik.errors.country}
            />
            <FormCard
              parentComponent={"editInvitation"}
              id="card_number"
              label="Card Number"
              format={"#### #### #### ####"}
              ShowImage
              value={formik.values.cardNumber}
              {...formik.getFieldProps("cardNumber")}
              error={formik.touched.cardNumber && formik.errors.cardNumber}
            />
            <FormField
              label="Name on card"
              id="name_on_card"
              parentComponent={"editInvitation"}
              {...formik.getFieldProps("cardName")}
              error={formik.touched.cardName && formik.errors.cardName}
            />
            <div className="flex gap-x-2">
              {/* <FormField
                label="Exp (MM/YY)"
                {...formik.getFieldProps("expiryDate")}
                error={formik.touched.expiryDate && formik.errors.expiryDate}
              /> */}
              <FormMask
                label="Expiry Date"
                id="expiry_date"
                // mask={expDateMask}
                guide={false}
                format="##/##"
                {...formik.getFieldProps("expiryDate")}
                error={formik.touched.expiryDate && formik.errors.expiryDate}
                success={formik.touched.expiryDate && !formik.errors.expiryDate}
                // placeholder={"MM/YY"}
              />
              {/* <FormField
                label="CVV"
                {...formik.getFieldProps("CVV")}
                error={formik.touched.CVV && formik.errors.CVV}
              /> */}
              <FormMask
                label="CVV"
                id="cvv"
                // mask={cvv}
                guide={false}
                type="password"
                {...formik.getFieldProps("CVV")}
                error={formik.touched.CVV && formik.errors.CVV}
                success={formik.touched.CVV && !formik.errors.CVV}
              />
            </div>
          </div>
        </div>
        {!paidTip && (
          <div className="flex flex-col gap-4 px-6 pt-6 pb-[32px] border-b-[1px] border-[#D8E2F0]">
            <p className="text-2xl font-semibold leading-8 gap-2">
              Add a tip <span className="font-normal">(optional)</span>
            </p>
            <div className="flex flex-col gap-[16px]">
              <div className="flex gap-[12px] justify-between items-center">
                {tipAmounts.map((tip) => (
                  <div
                    key={tip.id}
                    className={`h-[50px] w-full flex flex-col border-[1px] px-[13px] rounded-md text-center cursor-pointer ${
                      selectedTipId === tip.id
                        ? "border-[#00A6A2] bg-[#E5F6F6]"
                        : "border-[#CCCCCC]"
                    } relative overflow-hidden`}
                    onClick={() => handleTipSelection(tip.id)}
                  >
                    {selectedTipId === tip.id && (
                      <span className="h-[14px] w-[14px] bg-[#00A6A2] flex justify-center items-center absolute right-0 top-0 rounded-bl-[6px]">
                        <Image
                          src={CloseIcon}
                          alt="Icon"
                          width={6}
                          height={6}
                        />
                      </span>
                    )}
                    <p className="font-bold text-base leading-[22px]">
                      {tip.percentage}
                    </p>
                    <p className="text-sm font-normal leading-[19px]">
                      ({currencySymbol}
                      {tip.amount.toFixed(2)})
                    </p>
                  </div>
                ))}
              </div>
              {customTip ? (
                <div>
                  <FormField
                    label="Custom Tip(%)"
                    id="custom_tip"
                    parentComponent={"editInvitation"}
                    value={customTipAmount}
                    onChange={(e) => {
                      setSelectedTipId(0);
                      setTipAmount(0);
                      if (e.target.value > 99) {
                        setCustomTipError("Tip cannot be more than 99%");
                      } else {
                        setCustomTipError("");
                      }
                      setCustomTipAmount(e.target.value);
                    }}
                    error={customTipError}
                    type="number"
                  />
                  <div className="flex gap-2">
                    <button
                      className="flex-1 h-fit w-auto text-[#404040] py-2 border-[1px] border-[#CCCCCC] rounded-full font-bold text-base leading-5"
                      onClick={() => {
                        setCustomTip(false);
                        setCustomTipAmount();
                        setCustomTipError("");
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      className={`flex-1 bg-[#00A6A2] text-[#FFFFFF] font-bold text-base leading-[22px] py-2 h-auto w-full rounded-full ${
                        (!customTipAmount ||
                          customTipError ||
                          customTipAmount === formData?.customTipAmount ||
                          (selectedTipId === 0 && tipAmount !== 0)) &&
                        "opacity-50 cursor-not-allowed bg-[#E9E9E9] text-[#bebcbc]"
                      } transition duration-300 `}
                      onClick={() => {
                        setTipAmount((Number(customTipAmount) * amount) / 100);
                        setSelectedTipId(0);
                      }}
                      disabled={
                        !customTipAmount ||
                        customTipError ||
                        customTipAmount === formData?.customTipAmount ||
                        (selectedTipId === 0 && tipAmount !== 0)
                      }
                    >
                      Add
                    </button>
                  </div>
                </div>
              ) : (
                <button
                  className="h-[50px] text-[#404040] border-[1px] border-[#CCCCCC] rounded-md font-bold text-base leading-5"
                  onClick={() => setCustomTip(true)}
                >
                  Add custom tip
                </button>
              )}
            </div>
          </div>
        )}
        <div className="flex flex-col gap-4 px-6 pt-6 pb-[32px] border-b-[1px] border-[#D8E2F0]">
          <p className="text-2xl font-semibold leading-8 gap-2">
            Receipt
            {/* <span className="font-normal">(optional)</span> */}
          </p>
          <FormField
            label="Email address"
            name="email"
            id="email"
            isValid={!formik.errors.email && formik.values.email}
            {...formik.getFieldProps("email")}
          />
        </div>
        <div className="pt-6 px-6">
          <div className="flex flex-col gap-6 text-center pb-12">
            <Button
              handleClick={handleSubmit}
              isChecked={formik?.isValid}
              value={false ? "Submitting..." : "Next: Confirmation"}
            />
            <p className="text-[#00A6A2] underline font-xs font-semibold leading-4">
              Cancel and Return to [Merchant name]
            </p>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
}
