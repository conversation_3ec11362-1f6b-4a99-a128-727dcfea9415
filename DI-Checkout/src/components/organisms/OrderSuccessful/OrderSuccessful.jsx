"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import { useEffect, useState } from "react";

export default function OrderSuccessful() {
  const router = useRouter();

  const handleClick = () => {
    router.push("/thankYou");
  };

  //Commented For Demo purpose

  // useEffect(() => {
  //   const delay = setTimeout(() => {
  //     router.push("/thankyou");
  //   }, 3000);
  //   return () => clearTimeout(delay);
  // }, [router]);

  return (
    <div className="flex justify-center bg-black ">
      <div className="min-h-screen flex justify-center items-center bg-[#FFFFFF] w-[400px] px-10">
        <div className="text-center">
          <div className="flex justify-center">
            <Image
              src="/assets/Success_icon.svg"
              alt="Green Tick"
              width={100}
              height={100}
            />
          </div>
          <div className="text-[#404040] text-center ">
            <h1 className="text-2xl font-semibold mt-4 leading-8 ">
              Order Successful
            </h1>
            <p className="text-lg font-normal leading-[22px] mt-2">
              Thank you for your order.
            </p>
            {/* <p className="text-sm leading-5 font-normal mt-4 mx-10">
              If you&apos;re not redirected in a few seconds,{" "}
              <span
                className="text-[#00A6A2] cursor-pointer font-semibold"
                onClick={handleClick}
              >
                click here
              </span>
              .
            </p> */}
          </div>

          <div className="flex flex-col gap-4 mt-48 items-center">
            <Image
              className="mt-16"
              src="/assets/Vector.svg"
              alt="Lock"
              width={20}
              height={20}
            />
            <p className="text-xs text-[#767676] font-normal text-center">
              Secure checkout powered by NEMS
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
