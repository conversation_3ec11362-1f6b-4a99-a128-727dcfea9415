import CardDetails from "@/components/organisms/CardDetails/CardDetails";
import { Suspense } from "react";

const headers = {
  "Content-Type": "application/json",
};

export const callAPI = async (method, endpoint, payload) => {
  try {
    const res = await fetch(endpoint, {
      method,
      headers,
      body: JSON.stringify(payload),
    });
    const data = await res.json();
    return data;
  } catch (err) {
    return err;
  }
};
export default async function Home() {
  return (
    <Suspense>
      <CardDetails />
    </Suspense>
  );
}
