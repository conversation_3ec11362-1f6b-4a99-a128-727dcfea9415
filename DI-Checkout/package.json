{"name": "dine-in-checkout", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 8082", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "formik": "^2.4.6", "lucide-react": "^0.416.0", "next": "14.2.4", "react": "^18", "react-dom": "^18", "react-number-format": "^4.7.3", "sass": "^1.77.5", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1"}}